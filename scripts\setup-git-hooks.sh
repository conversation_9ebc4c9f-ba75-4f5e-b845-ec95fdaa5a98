#!/bin/bash
# Setup script to install git hooks for security

echo "🔧 Setting up Git hooks for Writer v2..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Error: Not in a Git repository root"
    exit 1
fi

# Create hooks directory if it doesn't exist
mkdir -p .git/hooks

# Install pre-commit hook for secrets detection
if [ -f "scripts/pre-commit-secrets-check.sh" ]; then
    cp scripts/pre-commit-secrets-check.sh .git/hooks/pre-commit
    chmod +x .git/hooks/pre-commit
    echo "✅ Pre-commit secrets check hook installed"
else
    echo "❌ Error: scripts/pre-commit-secrets-check.sh not found"
    exit 1
fi

# Create a commit-msg hook to check commit messages
cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash
# Commit message hook to ensure good commit practices

commit_regex='^(feat|fix|docs|style|refactor|perf|test|chore|ci)(\(.+\))?: .{1,60}$'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ Invalid commit message format!"
    echo ""
    echo "Commit message should follow Conventional Commits format:"
    echo "type(scope): description"
    echo ""
    echo "Types: feat, fix, docs, style, refactor, perf, test, chore, ci"
    echo "Example: feat(auth): add JWT token validation"
    echo "Example: fix(api): resolve 404 error on /api/health endpoint"
    echo ""
    exit 1
fi
EOF

chmod +x .git/hooks/commit-msg
echo "✅ Commit message format hook installed"

echo ""
echo "🎉 Git hooks setup complete!"
echo ""
echo "Installed hooks:"
echo "- Pre-commit: Secrets detection"
echo "- Commit-msg: Conventional commit format validation"
echo ""
echo "These hooks will run automatically on commit to help maintain code quality and security."
