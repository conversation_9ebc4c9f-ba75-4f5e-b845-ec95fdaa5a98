#!/bin/bash

# Pre-push Quality Checks
# Run this script before pushing to ensure your changes will pass CI

set -e

echo "🚀 Writer v2 - Pre-push Quality Checks"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to run a command and capture its result
run_check() {
    local check_name=$1
    local command=$2
    local working_dir=${3:-"."}
    
    print_status "info" "Running $check_name..."
    
    if [ "$working_dir" != "." ]; then
        cd "$working_dir"
    fi
    
    if eval "$command" > /dev/null 2>&1; then
        print_status "success" "$check_name passed"
        if [ "$working_dir" != "." ]; then
            cd - > /dev/null
        fi
        return 0
    else
        print_status "error" "$check_name failed"
        if [ "$working_dir" != "." ]; then
            cd - > /dev/null
        fi
        return 1
    fi
}

# Track overall status
overall_status=0

echo "1. Security Checks"
echo "=================="

# Run secrets detection
if [ -f "scripts/pre-commit-secrets-check.sh" ]; then
    if bash scripts/pre-commit-secrets-check.sh; then
        print_status "success" "Secrets detection passed"
    else
        print_status "error" "Secrets detected - please remove before pushing"
        overall_status=1
    fi
else
    print_status "warning" "Secrets check script not found"
fi

echo ""
echo "2. Backend Checks"
echo "================="

# Check if Python dependencies are installed
if ! python -c "import flake8, black, pytest" > /dev/null 2>&1; then
    print_status "warning" "Installing Python development dependencies..."
    pip install flake8 black pytest > /dev/null 2>&1
fi

# Backend linting
if ! run_check "Backend linting (critical errors)" "flake8 app/ main.py supabase_health_check.py --count --select=E9,F63,F7,F82 --show-source --statistics"; then
    overall_status=1
fi

# Backend code formatting
if ! run_check "Backend code formatting" "black --check app/ main.py supabase_health_check.py"; then
    print_status "info" "Run 'black app/ main.py supabase_health_check.py' to fix formatting"
    overall_status=1
fi

# Backend tests
if ! run_check "Backend tests" "pytest -v --tb=short"; then
    overall_status=1
fi

echo ""
echo "3. Frontend Checks"
echo "=================="

# Check if we're in the right directory and frontend exists
if [ ! -d "frontend" ]; then
    print_status "error" "Frontend directory not found"
    overall_status=1
else
    # Check if node_modules exists
    if [ ! -d "frontend/node_modules" ]; then
        print_status "info" "Installing frontend dependencies..."
        cd frontend && npm ci && cd ..
    fi
    
    # Frontend linting
    if ! run_check "Frontend linting" "npm run lint" "frontend"; then
        overall_status=1
    fi
    
    # Frontend API tests
    if ! run_check "Frontend API tests" "npm run test:api" "frontend"; then
        overall_status=1
    fi
    
    # Frontend environment tests
    if ! run_check "Frontend environment tests" "npm run test:env" "frontend"; then
        overall_status=1
    fi
    
    # Frontend coverage tests
    if ! run_check "Frontend coverage tests" "npm run test:coverage" "frontend"; then
        overall_status=1
    fi
fi

echo ""
echo "4. Docker Build Verification"
echo "============================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_status "warning" "Docker not found - skipping Docker build checks"
else
    # Docker build context security check
    print_status "info" "Checking Docker build context for secrets..."
    
    violations=0
    
    # Check backend build context
    for file in requirements.txt main.py supabase_health_check.py; do
        if [ -f "$file" ]; then
            if grep -qE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" "$file"; then
                print_status "error" "Secret detected in backend build context: $file"
                violations=1
            fi
        fi
    done
    
    # Check app directory
    if [ -d "app" ]; then
        if find app -type f -exec grep -lE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" {} \; | head -1 | grep -q .; then
            print_status "error" "Secret detected in backend app directory"
            violations=1
        fi
    fi
    
    # Check frontend build context
    if [ -d "frontend" ]; then
        find frontend -type f -not -path "*/node_modules/*" -not -path "*/build/*" -not -path "*/.git/*" | while read -r file; do
            if grep -qE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" "$file" 2>/dev/null; then
                print_status "error" "Secret detected in frontend build context: $file"
                violations=1
            fi
        done
    fi
    
    if [ $violations -eq 1 ]; then
        print_status "error" "Docker build context contains secrets"
        overall_status=1
    else
        print_status "success" "Docker build context security check passed"
    fi
    
    # Test Docker builds (optional - can be slow)
    if [ "${SKIP_DOCKER_BUILD:-false}" != "true" ]; then
        print_status "info" "Testing Docker builds (set SKIP_DOCKER_BUILD=true to skip)..."
        
        if docker build -f Dockerfile.backend -t test-backend . > /dev/null 2>&1; then
            print_status "success" "Backend Docker build test passed"
            docker rmi test-backend > /dev/null 2>&1
        else
            print_status "error" "Backend Docker build test failed"
            overall_status=1
        fi
        
        if docker build -f Dockerfile.frontend.prod -t test-frontend . > /dev/null 2>&1; then
            print_status "success" "Frontend Docker build test passed"
            docker rmi test-frontend > /dev/null 2>&1
        else
            print_status "error" "Frontend Docker build test failed"
            overall_status=1
        fi
    else
        print_status "info" "Skipping Docker build tests (SKIP_DOCKER_BUILD=true)"
    fi
fi

echo ""
echo "5. Summary"
echo "=========="

if [ $overall_status -eq 0 ]; then
    print_status "success" "All pre-push checks passed! 🎉"
    echo ""
    echo "Your changes are ready to be pushed and should pass CI."
    echo "You can now run: git push"
else
    print_status "error" "Some pre-push checks failed! 🚨"
    echo ""
    echo "Please fix the issues above before pushing."
    echo "Your changes will likely fail CI if pushed as-is."
    echo ""
    echo "Common fixes:"
    echo "  - Run 'black app/ main.py supabase_health_check.py' to fix Python formatting"
    echo "  - Run 'cd frontend && npm run lint -- --fix' to fix frontend linting issues"
    echo "  - Remove any secrets from source code and use environment variables instead"
    echo "  - Fix failing tests before pushing"
fi

exit $overall_status
