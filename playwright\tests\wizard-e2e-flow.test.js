import { test, expect } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'MK*9v9sseuu3#Z3qgypf'
};

const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:5000';

test.describe('Wizard E2E Flow - Login to Draft Creation', () => {
  let page;
  let context;

  test.beforeEach(async ({ browser }) => {
    // Create a new context and page for each test
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      recordVideo: { dir: 'test-results/videos/' }
    });
    
    page = await context.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      console.log(`[BROWSER CONSOLE ${msg.type().toUpperCase()}]:`, msg.text());
    });
    
    // Listen for page errors
    page.on('pageerror', error => {
      console.error('[<PERSON>GE ERROR]:', error.message);
    });
    
    // Track API requests and responses
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`[REQUEST] ${request.method()} ${request.url()}`);
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
        if (response.status() >= 400) {
          console.error(`[ERROR RESPONSE] ${response.status()} ${response.url()}`);
        }
      }
    });
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('should complete full login → wizard → draft workflow with 200 responses', async () => {
    console.log('🧪 Starting complete wizard workflow test...');
    
    // Step 1: Authentication
    console.log('Step 1: Authenticating user...');
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState('networkidle');
    
    // Fill login form
    await page.fill('#email, input[type="email"], input[name="email"]', TEST_USER.email);
    await page.fill('#password, input[type="password"], input[name="password"]', TEST_USER.password);
    
    // Submit login and wait for redirect
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    // Verify authentication succeeded
    const currentUrl = page.url();
    console.log(`✅ Authentication successful, redirected to: ${currentUrl}`);
    expect(currentUrl).not.toContain('/login');
    
    // Step 2: Navigate to Wizard
    console.log('Step 2: Navigating to wizard...');
    await page.goto(`${BASE_URL}/wizard`);
    await page.waitForLoadState('networkidle');
    
    // Verify wizard page loaded
    await expect(page.locator('h1, h2, [data-testid="page-title"]')).toContainText(/wizard|script/i);
    console.log('✅ Wizard page loaded successfully');
    
    // Step 3: Complete Wizard Flow
    console.log('Step 3: Starting wizard flow...');
    
    // Wait for business contexts to load
    await page.waitForTimeout(2000);
    
    // Step 3a: Fill initial form (Brainstorm step)
    console.log('Step 3a: Filling brainstorm form...');
    
    // Select business context (wait for dropdown to be populated)
    const businessContextSelect = page.locator('select[name="businessContext"], #business-context-select, [data-testid="business-context-select"]');
    await businessContextSelect.waitFor({ timeout: 5000 });
    await businessContextSelect.selectOption({ index: 1 }); // Select first available option
    
    // Fill content idea
    const contentIdeaInput = page.locator('input[name="contentIdea"], textarea[name="contentIdea"], #content-idea, [data-testid="content-idea"]');
    await contentIdeaInput.fill('How to use AI for small business marketing automation');
    
    // Click Next to start brainstorm
    await page.click('button:has-text("Next"), button[type="submit"]');
    
    // Wait for brainstorm API response
    await page.waitForTimeout(3000);
    
    // Step 3b: Select title from brainstorm results
    console.log('Step 3b: Selecting title from brainstorm results...');
    
    // Wait for radio buttons to appear
    const titleRadios = page.locator('input[type="radio"][name*="title"], input[type="radio"][value*="0"]');
    await titleRadios.first().waitFor({ timeout: 10000 });
    
    // Select first title option
    await titleRadios.first().check();
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(2000);
    
    // Step 3c: Select hook
    console.log('Step 3c: Selecting hook...');
    const hookRadios = page.locator('input[type="radio"][name*="hook"]');
    await hookRadios.first().waitFor({ timeout: 10000 });
    await hookRadios.first().check();
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(2000);
    
    // Step 3d: Select intro
    console.log('Step 3d: Selecting intro...');
    const introRadios = page.locator('input[type="radio"][name*="intro"]');
    await introRadios.first().waitFor({ timeout: 10000 });
    await introRadios.first().check();
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(2000);
    
    // Step 3e: Generate outline
    console.log('Step 3e: Generating outline...');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(5000); // Outline generation might take longer
    
    // Step 3f: Generate draft script
    console.log('Step 3f: Generating draft script...');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(10000); // Draft generation takes the longest
    
    // Step 4: Verify draft was created
    console.log('Step 4: Verifying draft creation...');
    
    // Look for draft content
    const draftContent = page.locator('pre, [data-testid="draft-content"], .draft-content');
    await draftContent.waitFor({ timeout: 15000 });
    
    const draftText = await draftContent.textContent();
    expect(draftText).toBeTruthy();
    expect(draftText.length).toBeGreaterThan(100); // Ensure substantial content
    
    console.log(`✅ Draft created successfully (${draftText.length} characters)`);
    
    // Step 5: Verify all API calls returned 200s
    console.log('Step 5: Verifying API response codes...');
    
    // Get all network responses
    const responses = await page.evaluate(() => {
      return window.performance.getEntriesByType('navigation').concat(
        window.performance.getEntriesByType('resource')
      ).filter(entry => entry.name.includes('/api/'));
    });
    
    // Check for any failed API calls
    const failedCalls = responses.filter(response => response.responseStatus >= 400);
    
    if (failedCalls.length > 0) {
      console.error('❌ Failed API calls detected:', failedCalls);
      throw new Error(`${failedCalls.length} API calls failed with status >= 400`);
    }
    
    console.log('✅ All API calls completed successfully with 200 status codes');
    
    // Step 6: Take final screenshot for verification
    await page.screenshot({ 
      path: 'test-results/wizard-complete-flow.png', 
      fullPage: true 
    });
    
    console.log('🎉 Complete wizard workflow test passed successfully!');
  });

  test('should handle wizard API errors gracefully', async () => {
    console.log('🧪 Testing wizard error handling...');
    
    // Login first
    await page.goto(`${BASE_URL}/login`);
    await page.fill('#email, input[type="email"]', TEST_USER.email);
    await page.fill('#password, input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    // Navigate to wizard
    await page.goto(`${BASE_URL}/wizard`);
    await page.waitForLoadState('networkidle');
    
    // Intercept wizard API calls and simulate errors
    await page.route('**/api/wizard/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated wizard API error' })
      });
    });
    
    // Try to start wizard flow
    await page.waitForTimeout(2000);
    
    const businessContextSelect = page.locator('select[name="businessContext"], #business-context-select');
    if (await businessContextSelect.count() > 0) {
      await businessContextSelect.selectOption({ index: 1 });
    }
    
    const contentIdeaInput = page.locator('input[name="contentIdea"], textarea[name="contentIdea"], #content-idea');
    if (await contentIdeaInput.count() > 0) {
      await contentIdeaInput.fill('Test error handling');
      await page.click('button:has-text("Next")');
      
      // Wait for error handling
      await page.waitForTimeout(3000);
      
      // Look for error messages
      const errorMessages = page.locator('[data-testid="error"], .error, .alert-error, .MuiAlert-standardError');
      const errorCount = await errorMessages.count();
      
      console.log(`Found ${errorCount} error message(s)`);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/wizard-error-handling.png', fullPage: true });
      
      // Verify error handling exists
      expect(errorCount).toBeGreaterThan(0);
    }
    
    console.log('✅ Wizard error handling test completed');
  });
});
