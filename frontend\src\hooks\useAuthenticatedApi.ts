import { useState, useCallback, useRef, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { api } from '../services/api';
import { logApiCall, updateApiCallLog } from '../utils/authRateLimitDebugger';

interface ApiCallOptions {
  retryCount?: number;
  retryDelay?: number;
  skipAuthCheck?: boolean;
}

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseAuthenticatedApiReturn<T> {
  state: ApiState<T>;
  execute: (endpoint: string, options?: ApiCallOptions) => Promise<T | null>;
  reset: () => void;
}

// Rate limiting for API calls per component
const componentCallTracker = new Map<string, { lastCall: number; callCount: number; failedCount: number }>();
const COMPONENT_CALL_LIMIT = 5; // Max calls per minute per component
const COMPONENT_CALL_WINDOW = 60000; // 1 minute
const COMPONENT_FAILED_LIMIT = 3; // Max failed calls before backing off

// Helper function to track failed requests
const trackFailedRequest = (id: string): void => {
  if (!id) return;

  const tracker = componentCallTracker.get(id);
  if (tracker) {
    tracker.failedCount++;
    console.log(`Failed request count for ${id}: ${tracker.failedCount}/${COMPONENT_FAILED_LIMIT}`);
  }
};

export function useAuthenticatedApi<T = any>(componentId?: string): UseAuthenticatedApiReturn<T> {
  const { session, loading: authLoading } = useAuth();
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (error?.response?.data?.error) return getErrorMessage(error.response.data.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };

  const checkRateLimit = useCallback((id: string): boolean => {
    if (!id) return true; // Skip rate limiting if no component ID

    const now = Date.now();
    const tracker = componentCallTracker.get(id);

    if (!tracker) {
      componentCallTracker.set(id, { lastCall: now, callCount: 1, failedCount: 0 });
      return true;
    }

    // Reset counter if window has passed
    if (now - tracker.lastCall > COMPONENT_CALL_WINDOW) {
      componentCallTracker.set(id, { lastCall: now, callCount: 1, failedCount: 0 });
      return true;
    }

    // Check if we're within limits
    if (tracker.callCount >= COMPONENT_CALL_LIMIT) {
      console.warn(`Rate limit exceeded for component ${id}`);
      return false;
    }

    // Check if too many failures - back off to prevent infinite loops
    if (tracker.failedCount >= COMPONENT_FAILED_LIMIT) {
      console.warn(`Too many failed requests for component ${id}, backing off`);
      return false;
    }

    // Increment counter
    tracker.callCount++;
    tracker.lastCall = now;
    return true;
  }, []);

  const execute = useCallback(async (
    endpoint: string,
    options: ApiCallOptions = {}
  ): Promise<T | null> => {
    const {
      retryCount = 2,
      retryDelay = 1000,
      skipAuthCheck = false,
    } = options;

    // Check authentication state unless explicitly skipped
    if (!skipAuthCheck && !authLoading && !session) {
      const error = 'Authentication required';
      setState(prev => ({ ...prev, error, loading: false }));
      throw new Error(error);
    }

    // Component-level rate limiting disabled - rely on global auth coordination
    // This prevents components from blocking themselves during auth refresh
    // if (componentId && !checkRateLimit(componentId)) {
    //   const error = 'Too many requests. Please wait before trying again.';
    //   setState(prev => ({ ...prev, error, loading: false }));
    //   throw new Error(error);
    // }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    // Log API call for debugging
    const logId = logApiCall(endpoint, componentId || 'unknown');
    let lastError: any;

    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        const response = await api.get(endpoint, {
          signal: abortControllerRef.current.signal,
        });

        const data = response.data;
        setState({ data, loading: false, error: null });

        // Log successful API call
        updateApiCallLog(logId, 'success');
        return data;

      } catch (error: any) {
        lastError = error;

        // Handle canceled requests gracefully - don't throw errors for them
        if (error.message === 'canceled' || error.code === 'ERR_CANCELED') {
          console.log(`Request canceled for ${endpoint}, not treating as error`);
          setState({ data: null, loading: false, error: null });
          return null; // Return null instead of throwing
        }

        // Don't retry on certain errors to prevent infinite loops
        if (
          error.name === 'AbortError' ||
          error.response?.status === 401 ||
          error.response?.status === 403 ||
          error.response?.status === 404 ||
          error.response?.status === 422 || // Unprocessable Entity
          error.response?.status >= 500 ||  // Server errors
          error.message?.includes('rate limit') ||
          error.message?.includes('Authentication refresh rate limited') ||
          error.message?.includes('refresh in backoff') ||
          error.message?.includes('Network Error') ||
          error.code === 'ECONNREFUSED' ||
          error.code === 'ENOTFOUND'
        ) {
          console.log(`Not retrying ${endpoint} due to error type:`, error.response?.status || error.code || error.message);
          break;
        }

        // Wait before retrying (with exponential backoff)
        if (attempt < retryCount) {
          const delay = retryDelay * Math.pow(2, attempt);
          console.log(`API call failed, retrying in ${delay}ms (attempt ${attempt + 1}/${retryCount + 1})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All retries failed
    const errorMessage = getErrorMessage(lastError);

    // Handle canceled requests gracefully - don't throw errors for them
    if (lastError?.message === 'canceled' || lastError?.code === 'ERR_CANCELED') {
      console.log(`All retries canceled for ${endpoint}, not treating as error`);
      setState({ data: null, loading: false, error: null });
      updateApiCallLog(logId, 'error', 'Request canceled');
      return null; // Return null instead of throwing
    }

    // Track failed request to prevent infinite loops
    if (componentId) {
      trackFailedRequest(componentId);
    }

    setState({ data: null, loading: false, error: errorMessage });

    // Log failed API call
    const isRateLimited = errorMessage.includes('rate limit') ||
                         errorMessage.includes('Authentication refresh rate limited') ||
                         errorMessage.includes('refresh in backoff');
    updateApiCallLog(logId, isRateLimited ? 'rate_limited' : 'error', errorMessage);

    // For 404 errors, don't throw to prevent refresh loops in components
    if (lastError?.response?.status === 404) {
      console.warn(`404 error for ${endpoint}, returning null to prevent refresh loop`);
      return null;
    }

    throw new Error(errorMessage);

  }, [session, authLoading, componentId, checkRateLimit]);

  const reset = useCallback(() => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  // Cleanup effect to prevent memory leaks and cancel requests on unmount
  useEffect(() => {
    return () => {
      // Cancel any ongoing request when component unmounts
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Clear component from rate limit tracker
      if (componentId) {
        componentCallTracker.delete(componentId);
      }
    };
  }, [componentId]);

  return {
    state,
    execute,
    reset,
  };
}

// Specialized hook for POST requests
export function useAuthenticatedApiPost<T = any>(componentId?: string) {
  const { session, loading: authLoading } = useAuth();
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (error?.response?.data?.error) return getErrorMessage(error.response.data.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };

  const execute = useCallback(async (
    endpoint: string,
    data: any,
    options: ApiCallOptions = {}
  ): Promise<T | null> => {
    const { skipAuthCheck = false } = options;

    if (!skipAuthCheck && !authLoading && !session) {
      const error = 'Authentication required';
      setState(prev => ({ ...prev, error, loading: false }));
      throw new Error(error);
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await api.post(endpoint, data);
      const responseData = response.data;
      setState({ data: responseData, loading: false, error: null });
      return responseData;
    } catch (error: any) {
      // Handle canceled requests gracefully - don't throw errors for them
      if (error.message === 'canceled' || error.code === 'ERR_CANCELED') {
        console.log(`POST request canceled for ${endpoint}, not treating as error`);
        setState({ data: null, loading: false, error: null });
        return null; // Return null instead of throwing
      }

      const errorMessage = getErrorMessage(error);
      setState({ data: null, loading: false, error: errorMessage });
      throw new Error(errorMessage);
    }
  }, [session, authLoading]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    state,
    execute,
    reset,
  };
}
