/**
 * Jest tests for Axios instance URL formation verification
 * Tests ensure proper API URL construction and prevent duplicate /api prefixes
 */

import axios from 'axios';
import { envConfig } from '../envConfig';

// Mock environment configuration
jest.mock('../envConfig', () => ({
  envConfig: {
    REACT_APP_API_URL: 'http://localhost:5000/api'
  }
}));

// Mock axios to capture actual requests
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('API URL Formation Tests', () => {
  let mockAxiosInstance: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create a mock axios instance
    mockAxiosInstance = {
      defaults: {
        baseURL: 'http://localhost:5000/api'
      },
      interceptors: {
        request: {
          use: jest.fn()
        },
        response: {
          use: jest.fn()
        }
      },
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    };

    // Mock axios.create to return our mock instance
    mockedAxios.create.mockReturnValue(mockAxiosInstance);
  });

  describe('Base URL Configuration', () => {
    test('should use environment API_URL with /api suffix', () => {
      // Import api service to trigger axios.create
      require('../api');
      
      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: 'http://localhost:5000/api'
        })
      );
    });

    test('should handle missing environment variable gracefully', () => {
      // Mock envConfig to return undefined
      jest.doMock('../envConfig', () => ({
        envConfig: {
          REACT_APP_API_URL: undefined
        }
      }));

      // Clear module cache and re-import
      jest.resetModules();
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: 'http://localhost:5000/api' // Should use fallback
        })
      );
    });
  });

  describe('URL Formation for API Calls', () => {
    let api: any;

    beforeEach(() => {
      // Import the api instance
      jest.resetModules();
      api = require('../api').api;
    });

    test('should form correct URLs for wizard endpoints', async () => {
      const mockResponse = { data: { session_id: 'test-123' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const { wizardBrainstorm } = require('../api');
      
      await wizardBrainstorm({
        business_context_id: 'test-context',
        content_idea: 'test idea'
      });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/wizard/brainstorm', // Should NOT have /api prefix
        expect.any(Object)
      );
    });

    test('should form correct URLs for chat endpoints', async () => {
      const mockResponse = { data: { sessions: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const { getChatSessions } = require('../api');
      
      await getChatSessions();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/ai_assistant/chat/sessions' // Should NOT have /api prefix
      );
    });

    test('should form correct URLs for business context endpoints', async () => {
      const mockResponse = { data: { contexts: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const { getBusinessContextsList } = require('../api');
      
      await getBusinessContextsList();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/business_contexts' // Should NOT have /api prefix
      );
    });

    test('should form correct URLs for content library endpoints', async () => {
      const mockResponse = { data: { items: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const { getContentItems } = require('../api');
      
      await getContentItems();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        '/content_library/items' // Should NOT have /api prefix
      );
    });
  });

  describe('URL Construction Edge Cases', () => {
    test('should handle baseURL with trailing slash', () => {
      // Mock envConfig with trailing slash
      jest.doMock('../envConfig', () => ({
        envConfig: {
          REACT_APP_API_URL: 'http://localhost:5000/api/'
        }
      }));

      jest.resetModules();
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: 'http://localhost:5000/api/' // Should preserve trailing slash
        })
      );
    });

    test('should handle baseURL without /api suffix', () => {
      // Mock envConfig without /api suffix
      jest.doMock('../envConfig', () => ({
        envConfig: {
          REACT_APP_API_URL: 'http://localhost:5000'
        }
      }));

      jest.resetModules();
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: 'http://localhost:5000' // Should use as-is
        })
      );
    });
  });

  describe('Full URL Resolution', () => {
    test('should resolve to correct full URLs', () => {
      const baseURL = 'http://localhost:5000/api';
      const endpoints = [
        '/wizard/brainstorm',
        '/ai_assistant/chat/sessions',
        '/business_contexts',
        '/content_library/items'
      ];

      endpoints.forEach(endpoint => {
        const fullURL = new URL(endpoint, baseURL).toString();
        expect(fullURL).toBe(`http://localhost:5000/api${endpoint}`);
        expect(fullURL).not.toContain('/api/api'); // No duplicate /api
      });
    });

    test('should prevent double /api prefixes in URLs', () => {
      const baseURL = 'http://localhost:5000/api';
      const problematicEndpoints = [
        '/api/wizard/brainstorm', // Wrong - has /api prefix
        '/api/chat/sessions',      // Wrong - has /api prefix
      ];

      problematicEndpoints.forEach(endpoint => {
        const fullURL = new URL(endpoint, baseURL).toString();
        expect(fullURL).toContain('/api/api'); // This would be wrong
      });

      // Correct endpoints should not have /api prefix
      const correctEndpoints = [
        '/wizard/brainstorm',
        '/chat/sessions'
      ];

      correctEndpoints.forEach(endpoint => {
        const fullURL = new URL(endpoint, baseURL).toString();
        expect(fullURL).not.toContain('/api/api'); // This is correct
      });
    });
  });

  describe('Environment-specific URL Formation', () => {
    const testCases = [
      {
        env: 'development',
        apiUrl: 'http://localhost:5000/api',
        expected: 'http://localhost:5000/api'
      },
      {
        env: 'production',
        apiUrl: 'https://api.example.com/api',
        expected: 'https://api.example.com/api'
      },
      {
        env: 'docker',
        apiUrl: 'http://backend:5000/api',
        expected: 'http://backend:5000/api'
      }
    ];

    testCases.forEach(({ env, apiUrl, expected }) => {
      test(`should handle ${env} environment correctly`, () => {
        jest.doMock('../envConfig', () => ({
          envConfig: {
            REACT_APP_API_URL: apiUrl
          }
        }));

        jest.resetModules();
        require('../api');

        expect(mockedAxios.create).toHaveBeenCalledWith(
          expect.objectContaining({
            baseURL: expected
          })
        );
      });
    });
  });

  describe('Request Configuration', () => {
    test('should include correct default headers', () => {
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        })
      );
    });

    test('should set appropriate timeout', () => {
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          timeout: 30000 // 30 seconds for AI responses
        })
      );
    });

    test('should configure credentials correctly', () => {
      require('../api');

      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          withCredentials: false
        })
      );
    });
  });
});
