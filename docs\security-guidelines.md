# Security Guidelines for Writer v2

## Overview

This document outlines the security measures implemented in Writer v2 to prevent accidental exposure of secrets and maintain secure development practices.

## Automated Security Checks

### 1. CI/CD Pipeline Security

Our GitHub Actions CI pipeline includes automated secret detection that will **block any PR or commit** containing:

- **OpenRouter API Keys**: `sk-or-v1-[64+ chars]`
- **OpenAI API Keys**: `sk-[48+ chars]`
- **JWT Tokens**: Long base64-encoded tokens
- **Supabase Keys**: `eyJ[100+ chars]`
- **YouTube API Keys**: `AIza[35 chars]`

### 2. Pre-commit Hooks

Install local git hooks to catch secrets before committing:

```bash
# Run from project root
bash scripts/setup-git-hooks.sh
```

This installs:
- **Pre-commit hook**: Scans staged files for secrets
- **Commit-msg hook**: Enforces conventional commit format

### 3. Manual Security Checks

Run security checks manually:

```bash
# From frontend directory
npm run security-check

# Or run the script directly
bash scripts/pre-commit-secrets-check.sh
```

## Environment Variable Security

### ✅ Safe Practices

1. **Use Template Files**: Copy `docs/env-template.txt` to create your local `.env.local`
2. **Frontend Variables**: Only expose public-safe values with `REACT_APP_` prefix
3. **Backend Secrets**: Keep all API keys and secrets in backend environment only
4. **Git Exclusion**: All `.env*` files are excluded from git (except templates)

### ❌ Dangerous Practices

1. **Never commit real API keys** in any file
2. **Never put server-side API keys** in frontend environment variables
3. **Never hardcode secrets** in source code
4. **Never commit `.env.local`** or similar files with real values

## Environment File Structure

### Backend Environment (`.env.local`)
```bash
# Backend-only secrets (NEVER expose to frontend)
JWT_SECRET_KEY=your-super-secret-jwt-key
OPENROUTER_API_KEY=your-openrouter-api-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Public values (safe to expose)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key  # This is public-safe
```

### Frontend Environment (`frontend/.env.local`)
```bash
# Only public-safe values with REACT_APP_ prefix
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SUPABASE_URL=https://your-project.supabase.co
REACT_APP_SUPABASE_KEY=your-supabase-anon-key

# NEVER put these in frontend:
# REACT_APP_OPENROUTER_API_KEY=xxx  ❌ WRONG!
# REACT_APP_JWT_SECRET=xxx          ❌ WRONG!
```

## Secret Rotation Process

If secrets are accidentally exposed:

1. **Immediately rotate all exposed keys**:
   - Supabase: Generate new keys in dashboard
   - OpenRouter: Generate new API key
   - YouTube: Generate new API key
   - JWT secrets: Generate new random strings

2. **Clean git history**:
   ```bash
   # Use BFG Repo-Cleaner or git filter-branch
   git filter-branch --force --index-filter \
     'git rm --cached --ignore-unmatch .env .env.local' \
     --prune-empty --tag-name-filter cat -- --all
   ```

3. **Force push cleaned history**:
   ```bash
   git push --force --all
   git push --force --tags
   ```

4. **Update all deployments** with new secrets

## Development Workflow

### Setting Up Local Environment

1. **Clone repository**
2. **Copy environment template**:
   ```bash
   cp docs/env-template.txt .env.local
   ```
3. **Fill in your actual values** in `.env.local`
4. **Install git hooks**:
   ```bash
   bash scripts/setup-git-hooks.sh
   ```
5. **Never commit `.env.local`** (it's in .gitignore)

### Before Committing

1. **Run security check**:
   ```bash
   npm run security-check  # From frontend directory
   ```
2. **Run linting**:
   ```bash
   npm run lint
   ```
3. **Commit with conventional format**:
   ```bash
   git commit -m "feat(auth): add JWT token validation"
   ```

## Incident Response

If you discover exposed secrets:

1. **Stop immediately** - don't commit more changes
2. **Create SECURITY_ALERT.md** documenting the exposure
3. **Rotate all exposed keys immediately**
4. **Clean git history** before continuing development
5. **Notify team members** to update their local environments

## Security Tools

- **GitHub Actions**: Automated CI secret detection
- **Pre-commit hooks**: Local secret detection
- **ESLint rules**: Code quality and security patterns
- **Git hooks**: Commit message format and security checks

## Contact

For security concerns or questions, contact the development team immediately.

---
**Last updated**: 2025-01-29 during Task 4: Security Hardening
