#!/usr/bin/env python3
"""
Integration test script to verify frontend-backend communication
Tests the complete API integration after route configuration fixes
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

class IntegrationTester:
    def __init__(self, backend_url: str = "http://localhost:5000", frontend_url: str = "http://localhost:3000"):
        self.backend_url = backend_url
        self.frontend_url = frontend_url
        self.session = requests.Session()
        self.auth_token = None
        
    def test_service_availability(self) -> Dict[str, bool]:
        """Test if both services are available"""
        print("🔍 Testing Service Availability")
        print("=" * 50)
        
        results = {}
        
        # Test backend
        try:
            response = self.session.get(f"{self.backend_url}/health", timeout=5)
            results['backend'] = response.status_code == 200
            print(f"Backend: {'✅ Available' if results['backend'] else '❌ Unavailable'} ({response.status_code})")
        except Exception as e:
            results['backend'] = False
            print(f"Backend: ❌ Unavailable ({e})")
        
        # Test frontend
        try:
            response = self.session.get(self.frontend_url, timeout=5)
            results['frontend'] = response.status_code == 200
            print(f"Frontend: {'✅ Available' if results['frontend'] else '❌ Unavailable'} ({response.status_code})")
        except Exception as e:
            results['frontend'] = False
            print(f"Frontend: ❌ Unavailable ({e})")
        
        return results
    
    def test_api_endpoints(self) -> Dict[str, Any]:
        """Test critical API endpoints"""
        print("\n🧪 Testing API Endpoints")
        print("=" * 50)
        
        endpoints = [
            ("GET", "/api/health", "Health Check"),
            ("GET", "/api/auth/ping", "Auth Ping"),
            ("POST", "/api/auth/register", "Auth Register", {"email": "<EMAIL>", "password": "testpass123"}),
            ("GET", "/api/business/", "Business Context"),
            ("GET", "/api/content/", "Content Library"),
            ("GET", "/api/chat/sessions", "Chat Sessions"),
            ("GET", "/api/activity/", "Activity Log"),
        ]
        
        results = []
        
        for endpoint_data in endpoints:
            method, path, name = endpoint_data[:3]
            data = endpoint_data[3] if len(endpoint_data) > 3 else None
            
            try:
                url = f"{self.backend_url}{path}"
                print(f"Testing {name}: {method} {path}")
                
                if method == "GET":
                    response = self.session.get(url, timeout=5)
                elif method == "POST":
                    response = self.session.post(url, json=data, timeout=5)
                else:
                    response = self.session.request(method, url, json=data, timeout=5)
                
                # Determine if response is expected
                expected_codes = [200, 201, 401, 403, 422]  # Valid responses
                is_success = response.status_code in expected_codes
                
                result = {
                    'name': name,
                    'method': method,
                    'path': path,
                    'status_code': response.status_code,
                    'success': is_success,
                    'response_size': len(response.content),
                    'has_json': self._is_json_response(response)
                }
                
                status_icon = "✅" if is_success else "❌"
                print(f"  {status_icon} {response.status_code} ({len(response.content)} bytes)")
                
                results.append(result)
                
            except Exception as e:
                result = {
                    'name': name,
                    'method': method,
                    'path': path,
                    'status_code': None,
                    'success': False,
                    'error': str(e)
                }
                print(f"  ❌ Error: {e}")
                results.append(result)
        
        return results
    
    def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and response formats"""
        print("\n🚨 Testing Error Handling")
        print("=" * 50)
        
        error_tests = [
            ("GET", "/api/nonexistent", "404 Not Found"),
            ("POST", "/api/auth/register", "422 Validation Error", {"email": "invalid", "password": "123"}),
            ("GET", "/api/business/protected", "401/403 Auth Error"),
        ]
        
        results = []
        
        for method, path, name, data in [(t[0], t[1], t[2], t[3] if len(t) > 3 else None) for t in error_tests]:
            try:
                url = f"{self.backend_url}{path}"
                print(f"Testing {name}: {method} {path}")
                
                if method == "GET":
                    response = self.session.get(url, timeout=5)
                else:
                    response = self.session.post(url, json=data, timeout=5)
                
                # Check if response has proper error format
                error_format_valid = False
                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        error_data = response.json()
                        # FastAPI error format should have 'detail' field
                        error_format_valid = 'detail' in error_data
                    except:
                        pass
                
                result = {
                    'name': name,
                    'status_code': response.status_code,
                    'error_format_valid': error_format_valid,
                    'has_json': self._is_json_response(response)
                }
                
                format_icon = "✅" if error_format_valid else "❌"
                print(f"  Status: {response.status_code}, Format: {format_icon}")
                
                results.append(result)
                
            except Exception as e:
                result = {
                    'name': name,
                    'status_code': None,
                    'error_format_valid': False,
                    'error': str(e)
                }
                print(f"  ❌ Error: {e}")
                results.append(result)
        
        return results
    
    def _is_json_response(self, response) -> bool:
        """Check if response is JSON"""
        try:
            response.json()
            return True
        except:
            return False
    
    def generate_report(self, service_results: Dict, api_results: list, error_results: list):
        """Generate comprehensive test report"""
        print("\n📊 INTEGRATION TEST REPORT")
        print("=" * 50)
        
        # Service availability
        backend_ok = service_results.get('backend', False)
        frontend_ok = service_results.get('frontend', False)
        print(f"Backend Service: {'✅ Available' if backend_ok else '❌ Unavailable'}")
        print(f"Frontend Service: {'✅ Available' if frontend_ok else '❌ Unavailable'}")
        
        # API endpoint results
        total_api_tests = len(api_results)
        successful_api_tests = sum(1 for r in api_results if r['success'])
        print(f"\nAPI Endpoints: {successful_api_tests}/{total_api_tests} successful")
        
        # Error handling results
        total_error_tests = len(error_results)
        valid_error_formats = sum(1 for r in error_results if r.get('error_format_valid', False))
        print(f"Error Handling: {valid_error_formats}/{total_error_tests} proper format")
        
        # Overall status
        overall_success = (
            backend_ok and 
            frontend_ok and 
            successful_api_tests >= total_api_tests * 0.8 and  # 80% API success
            valid_error_formats >= total_error_tests * 0.5     # 50% error format success
        )
        
        print(f"\n🎯 OVERALL STATUS: {'✅ INTEGRATION SUCCESSFUL' if overall_success else '❌ INTEGRATION ISSUES DETECTED'}")
        
        if not overall_success:
            print("\n🔧 RECOMMENDED ACTIONS:")
            if not backend_ok:
                print("  - Check backend container status and logs")
            if not frontend_ok:
                print("  - Check frontend container status and build")
            if successful_api_tests < total_api_tests * 0.8:
                print("  - Review API endpoint configuration and authentication")
            if valid_error_formats < total_error_tests * 0.5:
                print("  - Verify FastAPI error response format handling")
        
        return overall_success

def main():
    """Run integration tests"""
    print("🚀 Writer v2 Frontend-Backend Integration Test")
    print("=" * 60)
    
    tester = IntegrationTester()
    
    # Wait for services to be ready
    print("⏳ Waiting for services to be ready...")
    time.sleep(5)
    
    # Run tests
    service_results = tester.test_service_availability()
    api_results = tester.test_api_endpoints()
    error_results = tester.test_error_handling()
    
    # Generate report
    success = tester.generate_report(service_results, api_results, error_results)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
