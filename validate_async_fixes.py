#!/usr/bin/env python3
"""
Validation script to test the repository fixes work with real Supabase client.
Run this when Supabase is available to test content creation.
"""
import asyncio
import sys
import logging
import os

# Add project root to path
sys.path.insert(0, '/mnt/f/ValentinMarketing/Writerv2')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_content_creation_flow():
    """Test the actual content creation flow with real clients."""
    try:
        # Only run if environment variables are set
        if not all([
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        ]):
            logger.info("⚠️  Supabase environment variables not set - skipping real test")
            logger.info("This script should be run when Supabase is properly configured")
            return True
        
        logger.info("🚀 Testing with real Supabase client...")
        
        # Import dependencies
        from app.dependencies import get_content_item_repository, get_supabase_client
        
        # Get async client
        client = await get_supabase_client()
        logger.info(f"✅ Got Supabase client: {type(client)}")
        
        # Get repository
        repo = await get_content_item_repository(client)
        logger.info(f"✅ Got repository: {type(repo)}")
        
        # Test data
        test_data = {
            "user_id": "test-validation-user",
            "title": "Async Test Content",
            "content": "Testing async repository fixes",
            "content_type": "test",
            "tags": ["async", "validation"],
            "status": "draft"
        }
        
        # Create content item
        logger.info("📝 Creating test content item...")
        result = await repo.create_content_item(test_data)
        logger.info(f"✅ Content created successfully: {result.get('id')}")
        
        # Get the created item
        item_id = result.get('id')
        if item_id:
            logger.info("🔍 Retrieving created content item...")
            retrieved = await repo.get_by_id(item_id, "test-validation-user")
            logger.info(f"✅ Content retrieved successfully: {retrieved.get('title')}")
            
            # Clean up - delete the test item
            logger.info("🗑️  Cleaning up test content...")
            deleted = await repo.delete_content_item(item_id, "test-validation-user")
            logger.info(f"✅ Test content cleaned up: {deleted}")
        
        logger.info("🎉 All async repository operations completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Real test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_content_creation_flow())
    
    if result:
        print("\n✅ VALIDATION COMPLETE")
        print("🎉 Async repository pattern is working correctly!")
    else:
        print("\n❌ VALIDATION FAILED")
        print("Check the error messages above")
    
    sys.exit(0 if result else 1)