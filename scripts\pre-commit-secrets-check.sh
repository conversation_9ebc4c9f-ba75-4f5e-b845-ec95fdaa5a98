#!/bin/bash
# Pre-commit hook to detect secrets in staged files
# Install: cp scripts/pre-commit-secrets-check.sh .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit

echo "🔍 Checking staged files for secrets..."

# Define patterns for different types of secrets
OPENROUTER_PATTERN="sk-or-v1-[a-zA-Z0-9]{64,}"
OPENAI_PATTERN="sk-[a-zA-Z0-9]{48,}"
JWT_PATTERN="[A-Za-z0-9_-]{100,}\.[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}"
SUPABASE_KEY_PATTERN="eyJ[A-Za-z0-9_-]{100,}"
YOUTUBE_KEY_PATTERN="AIza[A-Za-z0-9_-]{35}"
GENERIC_SECRET_PATTERN="(secret|key|token|password).*[=:]\s*['\"][a-zA-Z0-9_-]{20,}['\"]"

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

if [ -z "$STAGED_FILES" ]; then
    echo "✅ No staged files to check"
    exit 0
fi

# Function to check for secrets in a file
check_file_for_secrets() {
    local file="$1"
    local violations=0
    
    # Skip binary files and excluded directories
    if [[ "$file" == *"node_modules"* ]] || [[ "$file" == *".git"* ]] || [[ "$file" == *"venv"* ]]; then
        return 0
    fi
    
    # Skip if file doesn't exist (deleted files)
    if [ ! -f "$file" ]; then
        return 0
    fi
    
    # Check for OpenRouter API keys
    if grep -qE "$OPENROUTER_PATTERN" "$file"; then
        echo "❌ BLOCKED: OpenRouter API key detected in $file"
        violations=1
    fi
    
    # Check for OpenAI API keys
    if grep -qE "$OPENAI_PATTERN" "$file"; then
        echo "❌ BLOCKED: OpenAI API key detected in $file"
        violations=1
    fi
    
    # Check for JWT tokens
    if grep -qE "$JWT_PATTERN" "$file"; then
        echo "❌ BLOCKED: JWT token detected in $file"
        violations=1
    fi
    
    # Check for Supabase keys
    if grep -qE "$SUPABASE_KEY_PATTERN" "$file"; then
        echo "❌ BLOCKED: Supabase key detected in $file"
        violations=1
    fi
    
    # Check for YouTube API keys
    if grep -qE "$YOUTUBE_KEY_PATTERN" "$file"; then
        echo "❌ BLOCKED: YouTube API key detected in $file"
        violations=1
    fi
    
    # Check for generic secrets (warning only)
    if grep -qiE "$GENERIC_SECRET_PATTERN" "$file"; then
        echo "⚠️  WARNING: Potential secret pattern in $file"
        # Don't block on generic patterns, just warn
    fi
    
    return $violations
}

# Check all staged files
total_violations=0
for file in $STAGED_FILES; do
    if ! check_file_for_secrets "$file"; then
        ((total_violations++))
    fi
done

if [ $total_violations -gt 0 ]; then
    echo ""
    echo "🚨 COMMIT BLOCKED: $total_violations secret(s) detected in staged files!"
    echo ""
    echo "To fix this:"
    echo "1. Remove the secrets from your files"
    echo "2. Use environment variables instead (see docs/env-template.txt)"
    echo "3. Add the files to .gitignore if they should never be committed"
    echo ""
    echo "If you need to commit template files with placeholder secrets:"
    echo "1. Ensure they contain only placeholder values like 'your-api-key-here'"
    echo "2. Update the patterns in this script if needed"
    echo ""
    exit 1
else
    echo "✅ No secrets detected in staged files"
    exit 0
fi
