"""
FastAPI dependency injection for Writer v2.
This module provides FastAPI dependencies to replace Flask's global objects.
"""

import logging
from functools import lru_cache
from typing import Optional, Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import jwt

from supabase import create_client, Client
from supabase._async.client import create_client as create_async_client, AsyncClient

logger = logging.getLogger(__name__)

# Security scheme for JWT tokens
security = HTTPBearer()

# Global clients for singleton pattern
_supabase_client: Optional[AsyncClient] = None
_supabase_sync_client: Optional[Client] = None


class SupabaseClientError(Exception):
    """Exception raised for Supabase client errors."""
    pass


class User(BaseModel):
    """User model for dependency injection"""
    id: str
    email: str
    name: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    last_sign_in_at: Optional[str] = None


@lru_cache()
def get_settings():
    """Get cached application settings from config module"""
    from app.config import get_settings as _get_settings
    return _get_settings()


async def get_supabase_client() -> AsyncClient:
    """
    Get or create an async Supabase client instance.
    This replaces Flask's g.supabase_client pattern.
    """
    global _supabase_client
    
    if _supabase_client is not None:
        return _supabase_client
    
    settings = get_settings()
    
    if not settings.supabase_url or not settings.supabase_service_role_key:
        logger.error("Supabase URL or service key not configured")
        raise SupabaseClientError("Supabase URL or service key not configured")
    
    try:
        _supabase_client = await create_async_client(
            settings.supabase_url, 
            settings.supabase_service_role_key
        )
        logger.info("Supabase async client initialized")
        return _supabase_client
    except Exception as e:
        logger.error(f"Failed to initialize Supabase async client: {e}")
        raise SupabaseClientError(f"Failed to initialize Supabase async client: {e}")


def get_supabase_sync_client() -> Client:
    """
    Get or create a sync Supabase client instance.
    This replaces Flask's g.supabase_sync_client pattern.
    """
    global _supabase_sync_client
    
    if _supabase_sync_client is not None:
        return _supabase_sync_client
    
    settings = get_settings()
    
    if not settings.supabase_url or not settings.supabase_service_role_key:
        logger.error("Supabase URL or service key not configured")
        raise SupabaseClientError("Supabase URL or service key not configured")
    
    try:
        _supabase_sync_client = create_client(
            settings.supabase_url, 
            settings.supabase_service_role_key
        )
        logger.info("Supabase sync client initialized")
        return _supabase_sync_client
    except Exception as e:
        logger.error(f"Failed to initialize Supabase sync client: {e}")
        raise SupabaseClientError(f"Failed to initialize Supabase sync client: {e}")


async def verify_jwt_token(token: str) -> Optional[dict]:
    """
    Verify a Supabase JWT token and return decoded claims.
    This replaces the Flask-based token verification.
    """
    settings = get_settings()
    
    # Try to get JWT secret from environment
    supabase_jwt_secret = getattr(settings, 'supabase_jwt_secret', None)
    if not supabase_jwt_secret:
        logger.error("SUPABASE_JWT_SECRET not configured")
        return None
    
    try:
        decoded = jwt.decode(
            token,
            supabase_jwt_secret,
            algorithms=["HS256"],
            options={"verify_aud": False},
        )
        logger.debug(f"JWT decode successful for sub: {decoded.get('sub')}")
        return decoded
    except jwt.InvalidTokenError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error during JWT verification: {str(e)}")
        return None


async def get_current_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    supabase: Annotated[AsyncClient, Depends(get_supabase_client)]
) -> User:
    """
    FastAPI dependency to get the current authenticated user.
    This replaces Flask's @supabase_auth_required decorator.
    """
    token = credentials.credentials
    
    # Verify the JWT token
    decoded = await verify_jwt_token(token)
    if not decoded:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = decoded.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing user ID",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Try to get user from database
        user_response = await supabase.table("users").select("*").eq("id", user_id).execute()
        
        if user_response.data:
            user_data = user_response.data[0]
            return User(
                id=user_data["id"],
                email=user_data.get("email", ""),
                name=user_data.get("name"),
                created_at=user_data.get("created_at"),
                updated_at=user_data.get("updated_at"),
                last_sign_in_at=user_data.get("last_sign_in_at")
            )
        else:
            # Fallback to token claims
            return User(
                id=user_id,
                email=decoded.get("email", ""),
                name=decoded.get("name")
            )
    except Exception as e:
        logger.warning(f"Failed to fetch user from database, using token claims: {e}")
        return User(
            id=user_id,
            email=decoded.get("email", ""),
            name=decoded.get("name")
        )


async def get_current_user_id(
    user: Annotated[User, Depends(get_current_user)]
) -> str:
    """
    FastAPI dependency to get the current user's ID.
    This replaces Flask's get_current_user_id() function.
    """
    return user.id


def get_ai_generator():
    """
    Get AI generator with injected settings.
    This replaces Flask's current_app.config dependency in AI modules.
    """
    from app.ai_assistant.common import get_ai_generator as _get_ai_generator
    return _get_ai_generator()


# Repository factory functions
async def get_user_repository(
    supabase_client: Annotated[AsyncClient, Depends(get_supabase_client)]
):
    """Get user repository with async client."""
    from app.repositories.supabase_user_repository import UserRepository
    return UserRepository(supabase_client=supabase_client)


def get_business_context_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    """Get business context repository."""
    from app.repositories.supabase_business_context_repository import BusinessContextRepository
    return BusinessContextRepository()


async def get_content_item_repository(
    supabase_client: Annotated[AsyncClient, Depends(get_supabase_client)]
):
    """Get content item repository with async client."""
    from app.repositories.supabase_content_item_repository import ContentItemRepository
    return ContentItemRepository(supabase_client=supabase_client)


def get_activity_log_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.activity_log_repository import ActivityLogRepository
    return ActivityLogRepository()


def get_feedback_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.supabase_feedback_repository import FeedbackRepository
    return FeedbackRepository()


def get_chat_session_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.supabase_chat_session_repository import ChatSessionRepository
    return ChatSessionRepository()


def get_wizard_session_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.wizard_session_repository import WizardSessionRepository
    return WizardSessionRepository()


def get_audience_analysis_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.supabase_audience_analysis_repository import AudienceAnalysisRepository
    return AudienceAnalysisRepository()


def get_video_data_repository(
    supabase_client: Annotated[Client, Depends(get_supabase_sync_client)]
):
    from app.repositories.supabase_video_data_repository import VideoDataRepository
    return VideoDataRepository()


# Type aliases for dependency injection
CurrentUserType = Annotated[User, Depends(get_current_user)]
CurrentUserIdType = Annotated[str, Depends(get_current_user_id)]
SupabaseClientType = Annotated[AsyncClient, Depends(get_supabase_client)]
SupabaseSyncClientType = Annotated[Client, Depends(get_supabase_sync_client)]

# Repository type aliases
UserRepositoryType = Annotated[object, Depends(get_user_repository)]
BusinessContextRepositoryType = Annotated[object, Depends(get_business_context_repository)]
ContentItemRepositoryType = Annotated[object, Depends(get_content_item_repository)]
ActivityLogRepositoryType = Annotated[object, Depends(get_activity_log_repository)]
FeedbackRepositoryType = Annotated[object, Depends(get_feedback_repository)]
ChatSessionRepositoryType = Annotated[object, Depends(get_chat_session_repository)]
WizardSessionRepositoryType = Annotated[object, Depends(get_wizard_session_repository)]
AudienceAnalysisRepositoryType = Annotated[object, Depends(get_audience_analysis_repository)]
VideoDataRepositoryType = Annotated[object, Depends(get_video_data_repository)]
