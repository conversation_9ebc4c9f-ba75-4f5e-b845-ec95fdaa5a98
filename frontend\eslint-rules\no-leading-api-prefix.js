/**
 * ESLint custom rule: no-leading-api-prefix
 * 
 * This rule prevents API endpoint strings from starting with "/api" to avoid
 * double prefixing when the base URL already includes "/api".
 * 
 * Examples:
 * ❌ Bad: api.get("/api/chat/sessions")
 * ✅ Good: api.get("/chat/sessions")
 * 
 * ❌ Bad: fetch("/api/auth/login")
 * ✅ Good: fetch("/auth/login")
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow API endpoint strings that start with "/api"',
      category: 'Best Practices',
      recommended: true,
    },
    fixable: 'code',
    schema: [],
    messages: {
      noLeadingApiPrefix: 'API endpoint should not start with "/api" since the base URL already includes it. Use "{{suggested}}" instead.',
    },
  },

  create(context) {
    /**
     * Check if a string literal starts with "/api"
     * @param {string} value - The string value to check
     * @returns {boolean} - True if the string starts with "/api"
     */
    function startsWithApiPrefix(value) {
      return typeof value === 'string' && value.startsWith('/api');
    }

    /**
     * Get the suggested fix by removing the "/api" prefix
     * @param {string} value - The original string value
     * @returns {string} - The suggested string without "/api" prefix
     */
    function getSuggestedFix(value) {
      if (value === '/api') {
        return '/';
      }
      return value.substring(4); // Remove "/api" prefix
    }

    /**
     * Check if the node is likely an API endpoint call
     * @param {Node} node - The AST node
     * @returns {boolean} - True if this looks like an API call
     */
    function isLikelyApiCall(node) {
      const parent = node.parent;
      
      // Check for method calls like api.get(), api.post(), fetch(), etc.
      if (parent && parent.type === 'CallExpression') {
        const callee = parent.callee;
        
        // Direct function calls: fetch("/api/...")
        if (callee.type === 'Identifier' && 
            ['fetch', 'axios'].includes(callee.name)) {
          return true;
        }
        
        // Method calls: api.get("/api/..."), axios.post("/api/...")
        if (callee.type === 'MemberExpression') {
          const objectName = callee.object.name;
          const methodName = callee.property.name;
          
          // Common API client patterns
          if (objectName === 'api' || objectName === 'axios' || objectName === 'http') {
            return ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'].includes(methodName);
          }
        }
      }
      
      // Check for template literals in API calls
      if (parent && parent.type === 'TemplateLiteral') {
        return isLikelyApiCall(parent);
      }
      
      return false;
    }

    return {
      Literal(node) {
        // Only check string literals that look like API endpoints
        if (node.value && typeof node.value === 'string' && 
            startsWithApiPrefix(node.value) && 
            isLikelyApiCall(node)) {
          
          const suggested = getSuggestedFix(node.value);
          
          context.report({
            node,
            messageId: 'noLeadingApiPrefix',
            data: {
              suggested,
            },
            fix(fixer) {
              return fixer.replaceText(node, `"${suggested}"`);
            },
          });
        }
      },

      TemplateElement(node) {
        // Check template literal elements for "/api" prefix
        if (node.value && node.value.raw && 
            startsWithApiPrefix(node.value.raw) && 
            isLikelyApiCall(node)) {
          
          const suggested = getSuggestedFix(node.value.raw);
          
          context.report({
            node,
            messageId: 'noLeadingApiPrefix',
            data: {
              suggested,
            },
            fix(fixer) {
              return fixer.replaceText(node, suggested);
            },
          });
        }
      },
    };
  },
};
