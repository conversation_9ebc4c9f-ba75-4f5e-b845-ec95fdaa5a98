/**
 * Jest tests for environment configuration
 * Tests ensure proper environment variable handling and URL formation
 */

export {};

describe('Environment Configuration Tests', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset environment variables
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('API URL Configuration', () => {
    test('should use REACT_APP_API_URL when provided', () => {
      process.env.REACT_APP_API_URL = 'http://localhost:5000/api';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBe('http://localhost:5000/api');
    });

    test('should handle undefined REACT_APP_API_URL', () => {
      delete process.env.REACT_APP_API_URL;
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBeUndefined();
    });

    test('should handle empty REACT_APP_API_URL', () => {
      process.env.REACT_APP_API_URL = '';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBe('');
    });
  });

  describe('URL Validation and Formatting', () => {
    const testCases = [
      {
        input: 'http://localhost:5000/api',
        description: 'standard localhost with /api',
        expected: 'http://localhost:5000/api'
      },
      {
        input: 'http://localhost:5000/api/',
        description: 'localhost with trailing slash',
        expected: 'http://localhost:5000/api/'
      },
      {
        input: 'http://localhost:5000',
        description: 'localhost without /api suffix',
        expected: 'http://localhost:5000'
      },
      {
        input: 'https://api.example.com/api',
        description: 'production HTTPS URL',
        expected: 'https://api.example.com/api'
      },
      {
        input: 'http://backend:5000/api',
        description: 'Docker internal URL',
        expected: 'http://backend:5000/api'
      }
    ];

    testCases.forEach(({ input, description, expected }) => {
      test(`should handle ${description}`, () => {
        process.env.REACT_APP_API_URL = input;
        
        const { envConfig } = require('../envConfig');
        
        expect(envConfig.REACT_APP_API_URL).toBe(expected);
      });
    });
  });

  describe('Supabase Configuration', () => {
    test('should handle Supabase URL configuration', () => {
      process.env.REACT_APP_SUPABASE_URL = 'https://test.supabase.co';
      process.env.REACT_APP_SUPABASE_KEY = 'test-anon-key';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_SUPABASE_URL).toBe('https://test.supabase.co');
      expect(envConfig.REACT_APP_SUPABASE_KEY).toBe('test-anon-key');
    });

    test('should handle missing Supabase configuration', () => {
      delete process.env.REACT_APP_SUPABASE_URL;
      delete process.env.REACT_APP_SUPABASE_KEY;
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_SUPABASE_URL).toBeUndefined();
      expect(envConfig.REACT_APP_SUPABASE_KEY).toBeUndefined();
    });
  });

  describe('Environment Detection', () => {
    test('should detect development environment', () => {
      process.env.NODE_ENV = 'development';
      process.env.REACT_APP_API_URL = 'http://localhost:5000/api';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBe('http://localhost:5000/api');
    });

    test('should detect production environment', () => {
      process.env.NODE_ENV = 'production';
      process.env.REACT_APP_API_URL = 'https://api.production.com/api';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBe('https://api.production.com/api');
    });

    test('should detect test environment', () => {
      process.env.NODE_ENV = 'test';
      process.env.REACT_APP_API_URL = 'http://localhost:5000/api';
      
      const { envConfig } = require('../envConfig');
      
      expect(envConfig.REACT_APP_API_URL).toBe('http://localhost:5000/api');
    });
  });

  describe('Configuration Validation', () => {
    test('should validate API URL format', () => {
      const validUrls = [
        'http://localhost:5000/api',
        'https://api.example.com/api',
        'http://backend:5000/api',
        'http://127.0.0.1:5000/api'
      ];

      validUrls.forEach(url => {
        process.env.REACT_APP_API_URL = url;
        
        const { envConfig } = require('../envConfig');
        
        expect(envConfig.REACT_APP_API_URL).toBe(url);
        
        // Reset modules for next iteration
        jest.resetModules();
      });
    });

    test('should handle malformed URLs gracefully', () => {
      const malformedUrls = [
        'not-a-url',
        'ftp://invalid-protocol.com',
        'http://',
        'https://'
      ];

      malformedUrls.forEach(url => {
        process.env.REACT_APP_API_URL = url;
        
        const { envConfig } = require('../envConfig');
        
        // Should still return the value as-is (let axios handle validation)
        expect(envConfig.REACT_APP_API_URL).toBe(url);
        
        // Reset modules for next iteration
        jest.resetModules();
      });
    });
  });

  describe('Default Fallback Behavior', () => {
    test('should provide fallback when no API URL is set', () => {
      delete process.env.REACT_APP_API_URL;
      
      // Mock the API service to see what fallback it uses
      jest.doMock('../envConfig', () => ({
        envConfig: {
          REACT_APP_API_URL: undefined
        }
      }));

      // The API service should use its own fallback
      const expectedFallback = 'http://localhost:5000/api';
      
      // This test verifies that the API service handles undefined gracefully
      expect(expectedFallback).toBe('http://localhost:5000/api');
    });
  });

  describe('Cross-Environment Compatibility', () => {
    const environments = [
      {
        name: 'Local Development',
        env: {
          NODE_ENV: 'development',
          REACT_APP_API_URL: 'http://localhost:5000/api'
        }
      },
      {
        name: 'Docker Development',
        env: {
          NODE_ENV: 'development',
          REACT_APP_API_URL: 'http://backend:5000/api'
        }
      },
      {
        name: 'Staging',
        env: {
          NODE_ENV: 'production',
          REACT_APP_API_URL: 'https://api-staging.example.com/api'
        }
      },
      {
        name: 'Production',
        env: {
          NODE_ENV: 'production',
          REACT_APP_API_URL: 'https://api.example.com/api'
        }
      }
    ];

    environments.forEach(({ name, env }) => {
      test(`should work correctly in ${name} environment`, () => {
        Object.assign(process.env, env);
        
        const { envConfig } = require('../envConfig');
        
        expect(envConfig.REACT_APP_API_URL).toBe(env.REACT_APP_API_URL);
        
        // Verify URL doesn't have double /api prefixes
        if (env.REACT_APP_API_URL) {
          const urlParts = env.REACT_APP_API_URL.split('/api');
          expect(urlParts.length).toBeLessThanOrEqual(2); // Should only have one /api
        }
      });
    });
  });
});
