﻿import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Chip,
  Divider,
  CircularProgress,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Drawer,
  AppBar,
  Toolbar,
  Breadcrumbs,
  Link,
  Tooltip,
  Switch,
  FormControlLabel,
  Checkbox,
  Tab,
  Tabs,
  ListItemIcon,
  ListItemText,
  Skeleton,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  Save as SaveIcon,
  FormatBold as FormatBoldIcon,
  FormatItalic as FormatItalicIcon,
  FormatUnderlined as FormatUnderlinedIcon,
  FormatListBulleted as ListIcon,
  Info as InfoIcon,
  NavigateNext as NavigateNextIcon,
  Cancel as CancelIcon,
  FormatAlignLeft as FormatAlignLeftIcon,
  Visibility as VisibilityIcon,
  AutoAwesome as AutoAwesomeIcon,
  Warning as WarningIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  CheckBox as CheckBoxIcon,
  Chat as ChatIcon,
  LibraryBooks as LibraryBooksIcon,
  Code as CodeIcon,
  Preview as PreviewIcon,
  YouTube as YouTubeIcon,
  MoreVert as MoreVertIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
// Import the OpenRouter service
import {
  formatTranscriptWithAI,
  checkOpenRouterApiKeySync,
} from "../services/openRouterService";
import { useContentSelection } from "../contexts/ContentSelectionContext";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
// Import our custom MarkdownRenderer component
import MarkdownRenderer from "../components/MarkdownRenderer";
// Import our custom markdown styles
import "../styles/markdown.css";
// Import the API service
import { api } from "../services/api";
import { trackContentActivity } from "../services/activityService";
import Menu from "@mui/material/Menu";
import BusinessIcon from "@mui/icons-material/Business";
// Import Redux hooks and selector
import { useAppSelector } from "../store/hooks";
import { selectIsAuthenticated } from "../store/auth/authSlice";

// Add a no-op debug function that does nothing during production
const noopDebug = (...args: any[]) => {
  // No operation - this prevents any console logs in production
};

// Ensure inline debounce utility function exists
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Add a helper function to standardize tag parsing
const standardizeTags = (tags: string[] | string | null): string[] => {
  if (!tags) return [];

  // Already an array
  if (Array.isArray(tags)) {
    return tags.map(String).filter((t) => t.trim());
  }

  // Handle string format
  if (typeof tags === "string") {
    const trimmedTags = tags.trim();

    // Try to parse JSON array format
    if (trimmedTags.startsWith("[") && trimmedTags.endsWith("]")) {
      try {
        const parsed = JSON.parse(trimmedTags.replace(/'/g, '"'));
        if (Array.isArray(parsed)) {
          return parsed.map(String).filter((t) => t.trim());
        }
      } catch (e) {
        // Parsing failed, fall back to comma split
      }
    }

    // Default to comma-separated values
    return trimmedTags
      .split(",")
      .map((t) => t.trim())
      .filter((t) => t);
  }

  return [];
};

// Define interfaces for our data
interface ContentStats {
  views?: number;
  likes?: number;
  comments?: number;
}

interface ContentItem {
  id: string;
  title: string;
  content: string;
  content_type: string | null;
  tags: string[] | string | null;
  created_at: string;
  updated_at: string;
  // Additional fields for YouTube content
  channel?: string;
  stats?: ContentStats;
  transcript?: string;
  thumbnail_url?: string;
  is_business_context: boolean; // Added for Business Context flag
}

// Add new interface for editor state
interface EditorState {
  isEditing: boolean;
  currentContent: ContentItem | null;
  hasUnsavedChanges: boolean;
}

const ContentLibrary: React.FC = () => {
  // Utility function to safely extract string from error objects
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };

  // State for content items
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [itemActionLoading, setItemActionLoading] = useState<
    Record<string, boolean>
  >({}); // Per-item loading (e.g., for toggle/delete)
  const [isFetchingList, setIsFetchingList] = useState<boolean>(true); // Global initial fetch loading
  const [isSavingItem, setIsSavingItem] = useState<boolean>(false); // Global saving state
  const [isDeletingItems, setIsDeletingItems] = useState<boolean>(false); // Global delete selected state
  const [error, setError] = useState<string | null>(null);

  // State for dialog
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [currentItem, setCurrentItem] = useState<ContentItem | null>(null);

  // Form state
  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [contentType, setContentType] = useState<string>("");
  const [tags, setTags] = useState<string>("");

  // Filter state
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterType, setFilterType] = useState<string>("");
  const [sortOption, setSortOption] = useState<string>("updated_at_desc");

  // Notification state
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  }>({
    open: false,
    message: "",
    severity: "info",
  });

  // Additional form state for YouTube content
  const [channel, setChannel] = useState<string>("");
  const [stats, setStats] = useState<ContentStats>({});
  const [transcript, setTranscript] = useState<string>("");

  // Add new state for editor
  const [editorState, setEditorState] = useState<EditorState>({
    isEditing: false,
    currentContent: null,
    hasUnsavedChanges: false,
  });

  // Add state for side panel
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const sidebarWidth = 300;

  // Use the Redux selector for authentication status instead of local state
  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  // Add state for preview mode
  const [previewMode, setPreviewMode] = useState<boolean>(false);

  // Add state for Markdown preview
  const [showMarkdownPreview, setShowMarkdownPreview] =
    useState<boolean>(false);

  // Add state for AI formatting loading
  const [formattingLoading, setFormattingLoading] = useState<boolean>(false);

  // Add state for OpenRouter API key status
  const [openRouterApiKeyStatus, setOpenRouterApiKeyStatus] = useState<{
    isConfigured: boolean;
    message: string;
  }>({
    isConfigured: true,
    message: "",
  });

  // Add state for selection mode - setting it to true by default
  const [selectionMode, setSelectionMode] = useState(true);

  // Create temporary input state for smoother editing
  const [inputContent, setInputContent] = useState("");

  // Add state for thumbnail URL
  const [thumbnailUrl, setThumbnailUrl] = useState<string>("");

  // Add state for the "More Options" menu
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(
    null
  );
  const [openMenuId, setOpenMenuId] = React.useState<null | string>(null);

  const navigate = useNavigate();

  const {
    selectedContentIds,
    toggleContentSelection,
    clearContentSelection,
    isContentSelected,
  } = useContentSelection();

  const theme = useTheme();

  // Use effect to sync content and inputContent
  useEffect(() => {
    setInputContent(content);
  }, [content]);

  // Create a safe content update function that doesn't produce debug logs
  const updateContentSafely = useCallback((newContent: string) => {
    setContent(newContent);
    setEditorState((prev) => ({ ...prev, hasUnsavedChanges: true }));
  }, []);

  // Create debounced content change handler with longer debounce
  const debouncedContentChange = useCallback(
    debounce((value: string) => {
      updateContentSafely(value);
    }, 1500), // Increased to 1.5 seconds for much better performance
    [updateContentSafely]
  );

  // Define toolbar components for markdown editor
  const EditorToolbar: React.FC<{ onFormat: (tag: string) => void }> = ({
    onFormat,
  }) => {
    return (
      <Box
        sx={{
          p: 1,
          display: "flex",
          flexWrap: "wrap",
          gap: 1,
          borderBottom: "1px solid",
          borderColor: "divider",
          bgcolor: "background.paper",
        }}
      >
        <Tooltip title="Bold">
          <IconButton size="small" onClick={() => onFormat("**")}>
            <FormatBoldIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Italic">
          <IconButton size="small" onClick={() => onFormat("*")}>
            <FormatItalicIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Heading 1">
          <Button size="small" onClick={() => onFormat("# ")}>
            H1
          </Button>
        </Tooltip>
        <Tooltip title="Heading 2">
          <Button size="small" onClick={() => onFormat("## ")}>
            H2
          </Button>
        </Tooltip>
        <Tooltip title="Heading 3">
          <Button size="small" onClick={() => onFormat("### ")}>
            H3
          </Button>
        </Tooltip>
        <Tooltip title="Bullet List">
          <IconButton size="small" onClick={() => onFormat("- ")}>
            <ListIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Code">
          <IconButton size="small" onClick={() => onFormat("`")}>
            <CodeIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Blockquote">
          <Button size="small" onClick={() => onFormat("> ")}>
            Quote
          </Button>
        </Tooltip>
        <Tooltip title="Link">
          <Button size="small" onClick={() => onFormat("[](url)")}>
            Link
          </Button>
        </Tooltip>
      </Box>
    );
  };

  // Function to handle Markdown formatting
  const handleFormat = (tag: string) => {
    const textArea = document.getElementById(
      "markdown-editor"
    ) as HTMLTextAreaElement;
    if (!textArea) return;

    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    const selectedText = content.substring(start, end);
    let newText = "";

    if (
      tag === "# " ||
      tag === "## " ||
      tag === "### " ||
      tag === "- " ||
      tag === "> "
    ) {
      // For headings, lists, and blockquotes, add at the beginning of the line
      const beforeSelection = content.substring(0, start);
      const afterSelection = content.substring(end);
      const lineStart = beforeSelection.lastIndexOf("\n") + 1;

      newText =
        content.substring(0, lineStart) +
        tag +
        content.substring(lineStart, end) +
        afterSelection;

      // Set new selection
      setTimeout(() => {
        textArea.focus();
        const newCursorPos = lineStart + tag.length + selectedText.length;
        textArea.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    } else if (tag === "[](url)") {
      // Special case for links
      const link = selectedText ? `[${selectedText}](url)` : "[Link text](url)";

      newText = content.substring(0, start) + link + content.substring(end);

      // Set selection to url part for easy replacement
      setTimeout(() => {
        textArea.focus();
        if (selectedText) {
          // If text was selected, put cursor at the URL
          const urlStart = start + selectedText.length + 3;
          const urlEnd = urlStart + 3;
          textArea.setSelectionRange(urlStart, urlEnd);
        } else {
          // If no text was selected, select the default "Link text"
          const textStart = start + 1;
          const textEnd = textStart + 9;
          textArea.setSelectionRange(textStart, textEnd);
        }
      }, 0);
    } else {
      // For inline formatting like bold, italic, code
      if (selectedText) {
        // If text is selected, wrap it
        newText =
          content.substring(0, start) +
          tag +
          selectedText +
          tag +
          content.substring(end);

        // Keep the same selection, but expanded to include the tags
        setTimeout(() => {
          textArea.focus();
          textArea.setSelectionRange(start + tag.length, end + tag.length);
        }, 0);
      } else {
        // If no text is selected, insert paired tags and place cursor in between
        newText =
          content.substring(0, start) + tag + tag + content.substring(end);

        // Place cursor between the tags
        setTimeout(() => {
          textArea.focus();
          const newCursorPos = start + tag.length;
          textArea.setSelectionRange(newCursorPos, newCursorPos);
        }, 0);
      }
    }

    // Update content using our safe wrapper
    updateContentSafely(newText);
  };

  // Function to reset form
  const resetForm = useCallback(() => {
    setTitle("");
    setContent("");
    setContentType("");
    setTags("");
    setCurrentItem(null);
    setChannel("");
    setStats({});
    setTranscript("");
    setThumbnailUrl("");
    setDialogMode("add");
    setOpenDialog(false);
    setShowMarkdownPreview(false);
    setEditorState({
      isEditing: false,
      currentContent: null,
      hasUnsavedChanges: false,
    });
  }, []);

  // Suppress ResizeObserver loop error
  useEffect(() => {
    const originalError = console.error;
    console.error = (...args) => {
      if (
        args[0] &&
        typeof args[0] === "string" &&
        (args[0].includes("ResizeObserver loop") ||
          args[0].includes(
            "ResizeObserver loop completed with undelivered notifications"
          ))
      ) {
        return;
      }
      originalError.apply(console, args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  // Add a safeguard to prevent unexpectedly exiting edit mode
  useEffect(() => {
    if (formattingLoading && !editorState.isEditing) {
      console.warn(
        "Detected unexpected exit from edit mode during formatting, restoring..."
      );
      setEditorState((prev) => ({
        ...prev,
        isEditing: true,
      }));
    }
  }, [formattingLoading, editorState.isEditing]);

  // Effect to handle page unload and auto-save
  useEffect(() => {
    if (isAuthenticated) {
      fetchContentItems();
    }

    // Force save when component unmounts if there are unsaved changes
    return () => {
      if (
        editorState.hasUnsavedChanges &&
        editorState.isEditing &&
        content &&
        content.trim().length > 0
      ) {
        if (editorState.currentContent) {
          // Prepare the content for saving
          let finalContent = content;
          if (contentType === "youtube_video") {
            const sections = [];
            sections.push(`Title: ${title}`);
            if (channel) sections.push(`Channel: ${channel}`);

            if (Object.keys(stats).length > 0) {
              const statsLines = ["Stats:"];
              if (stats.views)
                statsLines.push(`Views: ${stats.views.toLocaleString()}`);
              if (stats.likes)
                statsLines.push(`Likes: ${stats.likes.toLocaleString()}`);
              if (stats.comments)
                statsLines.push(`Comments: ${stats.comments.toLocaleString()}`);
              sections.push(statsLines.join("\n"));
            }

            // Add thumbnail URL if available
            if (thumbnailUrl) {
              sections.push(`Thumbnail: ${thumbnailUrl}`);
            }

            sections.push(`Transcript:\n${content}`);
            finalContent = sections.join("\n\n");
          }

          // Use the API service instead of direct fetch
          api
            .put(`/content/${editorState.currentContent.id}`, {
              title,
              content: finalContent,
              content_type: contentType || null,
              tags: tags ? tags.split(",") : null,
            })
            .catch((error) => {
              console.error("Error saving content on unmount:", error);
            });
        }
      }
    };
  }, [
    isAuthenticated,
    editorState.hasUnsavedChanges,
    editorState.isEditing,
    editorState.currentContent,
    content,
    contentType,
    title,
    channel,
    stats,
    thumbnailUrl,
    tags,
  ]);

  // Check OpenRouter API key when component mounts
  useEffect(() => {
    const status = checkOpenRouterApiKeySync();
    setOpenRouterApiKeyStatus(status);

    if (!status.isConfigured) {
      // Use a warning only for important configuration issues
      console.warn("OpenRouter API key issue:", status.message);
    }
  }, []);

  // Function to fetch content items - UPDATED TO USE API SERVICE
  const fetchContentItems = useCallback(async () => {
    setIsFetchingList(true);
    setError(null); // Reset error on fetch
    try {
      const queryParams = new URLSearchParams();
      if (searchTerm) queryParams.append("search", searchTerm);
      if (filterType) queryParams.append("content_type", filterType);
      // Add sort parameters based on sortOption state
      let sortBy = "updated_at"; // Default sort column
      let sortOrder = "desc"; // Default sort order

      switch (sortOption) {
        case "updated_at_desc":
          sortBy = "updated_at";
          sortOrder = "desc";
          break;
        case "updated_at_asc":
          sortBy = "updated_at";
          sortOrder = "asc";
          break;
        case "title_asc":
          sortBy = "title";
          sortOrder = "asc";
          break;
        case "title_desc":
          sortBy = "title";
          sortOrder = "desc";
          break;
        // No default case needed as Select limits options, but defaults above handle initial state
      }
      queryParams.append("sort_by", sortBy);
      queryParams.append("sort_order", sortOrder);

      // Use API service instead of direct fetch
      const response = await api.get(`/api/content/?${queryParams.toString()}`);

      // Properly extract content_items array from response
      // Check if response.data and response.data.content_items exist and is an array
      if (response?.data && Array.isArray(response.data.content_items)) {
        setContentItems(response.data.content_items);
        clearContentSelection(); // Clear selection when filters/data change
      } else {
        // Handle cases where the structure is different or empty
        console.warn(
          "Unexpected response structure or empty content_items:",
          response?.data
        );
        setContentItems([]); // Set to empty array if data is missing/malformed
        clearContentSelection();
      }
    } catch (error: any) {
      // Use 'any' for broader error handling initially
      console.error("Error fetching content items:", error);
      // Try to get a more specific error message from the backend response
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Failed to load content. Please try again.";
      setError(errorMessage); // Set the specific error message
      setContentItems([]); // Clear items on error
    } finally {
      setIsFetchingList(false);
    }
  }, [searchTerm, filterType, sortOption, clearContentSelection]); // Added sortOption to dependency array

  // Fetch content items on mount and when filters change
  useEffect(() => {
    // Now using Redux-based authentication
    if (isAuthenticated) {
      fetchContentItems();
    }
  }, [fetchContentItems, isAuthenticated]);

  // Function to handle dialog open for adding new content
  const handleAddClick = () => {
    setDialogMode("add");
    setTitle("");
    setContent("");
    setContentType("");
    setTags("");
    setChannel("");
    setStats({});
    setTranscript("");

    // Set editor state to editing mode for new content
    setEditorState({
      isEditing: true,
      currentContent: null,
      hasUnsavedChanges: false,
    });

    // Important: Open the dialog explicitly
    setOpenDialog(true);
  };

  // Function to extract YouTube thumbnail URL from content
  const extractYouTubeThumbnail = (content: string): string => {
    const thumbnailMatch = content.match(/Thumbnail:\s*([^\n]+)/);
    if (thumbnailMatch && thumbnailMatch[1]) {
      return thumbnailMatch[1].trim();
    }
    return "";
  };

  // Function to handle dialog open for editing content - UPDATED TO USE API SERVICE
  const handleEditClick = async (item: ContentItem) => {
    // Set initial UI state
    setDialogMode("edit");
    setCurrentItem(item);
    setTitle(item.title);
    setContentType(item.content_type || "");

    // Open the dialog explicitly
    setOpenDialog(true);

    // Fetch the latest version of this content item directly from the server
    try {
      // Use API service instead of direct fetch
      const response = await api.get(`/api/content/${item.id}`);

      if (response.data) {
        // Get the item from response, properly handling both formats:
        // { content_item: {...} } or directly {...}
        const latestItem = response.data.content_item || response.data;

        // Update our local item with the latest data
        item = latestItem;
        setCurrentItem(latestItem);
        setTitle(latestItem.title);
        setContentType(latestItem.content_type || "");
      }
    } catch (error) {
      // Only log actual errors
      console.error("Error fetching latest content:", error);
      // Continue with the cached version if fetch fails
    }

    // Handle tags with proper type checking
    const tagArray = standardizeTags(item.tags);
    setTags(tagArray.join(","));

    // Set editor state to editing mode with the current content FIRST
    // This ensures we don't lose the editing state if formatting fails
    setEditorState({
      isEditing: true,
      currentContent: item,
      hasUnsavedChanges: false,
    });

    // Parse content sections if it's a YouTube video
    if (item.content_type === "youtube_video") {
      // Extract the transcript section from content
      let extractedTranscript = "";

      // Try different patterns to extract transcript
      const transcriptMatch1 = item.content.match(
        /Transcript:([\s\S]*?)(?=$|Title:|Channel:|Stats:|Thumbnail:)/
      );
      const transcriptMatch2 = item.content.match(/Transcript:([\s\S]*?)$/);
      const lastTranscriptIndex = item.content.lastIndexOf("Transcript:");

      if (transcriptMatch1 && transcriptMatch1[1]) {
        extractedTranscript = transcriptMatch1[1].trim();
      } else if (transcriptMatch2 && transcriptMatch2[1]) {
        extractedTranscript = transcriptMatch2[1].trim();
      } else if (lastTranscriptIndex !== -1) {
        extractedTranscript = item.content
          .substring(lastTranscriptIndex + "Transcript:".length)
          .trim();
      } else {
        // Fallback to section-based approach
        const sections = item.content.split("\n\n");

        // Look for a section that starts with Transcript:
        for (const section of sections) {
          if (section.startsWith("Transcript:")) {
            extractedTranscript = section.replace("Transcript:", "").trim();
            break;
          }
        }

        // If still no transcript, try one more approach
        if (!extractedTranscript) {
          const transcriptParts = item.content.split("Transcript:");
          if (transcriptParts.length > 1) {
            extractedTranscript =
              transcriptParts[transcriptParts.length - 1].trim();
          }
        }
      }

      // If we still don't have a transcript, use the original content
      if (!extractedTranscript) {
        extractedTranscript = item.content;
      }

      // Extract channel information
      const channelMatch = item.content.match(/Channel:\s*([^\n]+)/);
      if (channelMatch && channelMatch[1]) {
        setChannel(channelMatch[1].trim());
      }

      // Extract stats information
      const statsSection = item.content.match(/Stats:([\s\S]*?)(?=\n\n|$)/);
      if (statsSection && statsSection[1]) {
        const statsText = statsSection[1].trim();
        const viewsMatch = statsText.match(/Views:\s*([\d,]+)/);
        const likesMatch = statsText.match(/Likes:\s*([\d,]+)/);
        const commentsMatch = statsText.match(/Comments:\s*([\d,]+)/);

        const newStats: ContentStats = {};

        if (viewsMatch && viewsMatch[1]) {
          newStats.views = parseInt(viewsMatch[1].replace(/,/g, ""), 10);
        }

        if (likesMatch && likesMatch[1]) {
          newStats.likes = parseInt(likesMatch[1].replace(/,/g, ""), 10);
        }

        if (commentsMatch && commentsMatch[1]) {
          newStats.comments = parseInt(commentsMatch[1].replace(/,/g, ""), 10);
        }

        setStats(newStats);
      }

      // Extract thumbnail URL
      const thumbnailUrl = extractYouTubeThumbnail(item.content);
      setThumbnailUrl(thumbnailUrl);

      // Set the content to just the transcript
      setContent(extractedTranscript);
      setTranscript(extractedTranscript);
    } else {
      // For non-YouTube content, just use the content as is
      setContent(item.content);
      // Reset YouTube-specific fields
      setChannel("");
      setStats({});
      setThumbnailUrl("");
    }
  };

  // Function to handle dialog close
  const handleDialogClose = () => {
    setOpenDialog(false);

    // Also reset editor state when closing dialog
    setEditorState({
      isEditing: false,
      currentContent: null,
      hasUnsavedChanges: false,
    });
  };

  // Function to handle formatting transcripts with AI
  const handleFormatTranscript = async () => {
    if (!transcript && !content) {
      setNotification({
        open: true,
        message: "No transcript content to format",
        severity: "warning",
      });
      return;
    }

    setFormattingLoading(true);
    try {
      // Check if OpenRouter API key is configured
      const keyStatus = await checkOpenRouterApiKeySync();
      if (!keyStatus.isConfigured) {
        setOpenRouterApiKeyStatus(keyStatus);
        setNotification({
          open: true,
          message:
            "OpenRouter API key not configured. Please set it in the settings.",
          severity: "error",
        });
        setFormattingLoading(false);
        return;
      }

      // Format the transcript
      const textToFormat = transcript || content;
      const formattedContent = await formatTranscriptWithAI(textToFormat);

      if (formattedContent) {
        // Use our safe wrapper to update content
        updateContentSafely(formattedContent);
        setNotification({
          open: true,
          message: "Transcript formatted successfully",
          severity: "success",
        });
      } else {
        throw new Error("Failed to format transcript");
      }
    } catch (error) {
      console.error("Error formatting transcript:", error);
      setNotification({
        open: true,
        message: "Error formatting transcript. Please try again.",
        severity: "error",
      });
    } finally {
      setFormattingLoading(false);
    }
  };

  // Function to handle form submission - UPDATED TO USE API SERVICE
  const handleSubmit = useCallback(async () => {
    if (!title.trim() || !content.trim()) {
      setNotification({
        open: true,
        message: "Title and content are required",
        severity: "error",
      });
      return;
    }

    // Use isSaving state
    setIsSavingItem(true);
    try {
      // Prepare content data
      const contentData: Partial<ContentItem> = {
        title: title.trim(),
        content: content.trim(),
        content_type: contentType || null,
        tags: standardizeTags(tags) || null,
      };

      // Add YouTube specific data if applicable
      if (contentType === "youtube_video") {
        contentData.channel = channel;
        contentData.stats = stats;
        contentData.transcript = transcript;
        contentData.thumbnail_url = thumbnailUrl;

        // Format content with proper sections for YouTube videos
        const sections = [];
        sections.push(`Title: ${title.trim()}`);
        if (channel) sections.push(`Channel: ${channel}`);

        if (Object.keys(stats).length > 0) {
          const statsLines = ["Stats:"];
          if (stats.views)
            statsLines.push(`Views: ${stats.views.toLocaleString()}`);
          if (stats.likes)
            statsLines.push(`Likes: ${stats.likes.toLocaleString()}`);
          if (stats.comments)
            statsLines.push(`Comments: ${stats.comments.toLocaleString()}`);
          sections.push(statsLines.join("\n"));
        }

        // Add thumbnail URL if available
        if (thumbnailUrl) {
          sections.push(`Thumbnail: ${thumbnailUrl}`);
        }

        sections.push(`Transcript:\n${content.trim()}`);
        contentData.content = sections.join("\n\n");
      }

      let response;
      if (editorState.currentContent?.id) {
        // Update existing content
        response = await api.put(
          `/api/content/${editorState.currentContent.id}`,
          contentData
        );

        // Track content update
        try {
          await trackContentActivity(
            editorState.currentContent.id.toString(),
            contentData.title || "Content",
            "Update"
          );
        } catch (activityError) {
          console.error(
            "Failed to track content update activity:",
            activityError
          );
          // Non-blocking - continue even if tracking fails
        }
      } else {
        // Create new content
        response = await api.post("/content", contentData);

        // Track content creation
        try {
          await trackContentActivity(
            response.data.id.toString(),
            contentData.title || "Content",
            "Creation"
          );
        } catch (activityError) {
          console.error(
            "Failed to track content creation activity:",
            activityError
          );
          // Non-blocking - continue even if tracking fails
        }
      }

      // Refresh content list
      fetchContentItems();

      // Reset form and close dialog
      resetForm();
      setOpenDialog(false);

      // Show success notification
      setNotification({
        open: true,
        message:
          dialogMode === "add"
            ? "Content added successfully"
            : "Content updated successfully",
        severity: "success",
      });
      setEditorState((prev) => ({ ...prev, hasUnsavedChanges: false })); // Reset unsaved changes flag
    } catch (error) {
      console.error("Error saving content:", error);
      setNotification({
        open: true,
        message: "Error saving content. Please try again.",
        severity: "error",
      });
    } finally {
      // setLoading(false); // REMOVED - Use isSaving
      setIsSavingItem(false);
    }
  }, [
    title,
    content,
    contentType,
    tags,
    channel,
    stats,
    transcript,
    thumbnailUrl,
    dialogMode,
    editorState.currentContent,
    fetchContentItems,
    resetForm,
  ]);

  // Function to handle content deletion - UPDATED TO USE API SERVICE
  const handleDeleteContent = async (id: string) => {
    // Directly execute the delete logic since dialog is disabled
    setItemActionLoading((prev) => ({ ...prev, [id]: true }));
    setError(null);
    try {
      await api.delete(`/api/content/${id}`);
      setContentItems((prevItems) =>
        prevItems.filter((item) => item.id !== id)
      );
      if (isContentSelected(id)) {
        toggleContentSelection(id);
      }
      showNotification("Content item deleted successfully", "success");
      if (editorState.currentContent?.id === id) {
        handleCancelEdit(); // Close editor if deleted item was open
      }
    } catch (err: any) {
      setError(err.message || "Failed to delete content item");
      showNotification(
        `Error deleting item: ${err.message || "Unknown error"}`,
        "error"
      );
    } finally {
      setItemActionLoading((prev) => ({ ...prev, [id]: false }));
    }
  };

  // Function to handle toggling the business context flag
  const handleToggleBusinessContext = async (
    itemId: string,
    currentStatus: boolean
  ) => {
    const newItemStatus = !currentStatus;
    // Directly execute the toggle logic since dialog is disabled
    setItemActionLoading((prev) => ({ ...prev, [itemId]: true }));
    setError(null);
    const originalItems = contentItems; // Keep original state for rollback
    setContentItems(
      (
        prevItems // Optimistic update
      ) =>
        prevItems.map((item) =>
          item.id === itemId
            ? { ...item, is_business_context: !currentStatus }
            : item
        )
    );
    try {
      await api.put(`/api/content/${itemId}`, {
        is_business_context: !currentStatus,
      });
      showNotification(`Business context updated successfully`, "success");
      trackContentActivity(
        itemId,
        !currentStatus
          ? "marked_as_business_context"
          : "unmarked_as_business_context",
        contentItems.find((i) => i.id === itemId)?.content_type || "unknown"
      );
      // Update editor state if the item is currently being edited
      if (editorState.currentContent?.id === itemId) {
        setEditorState((prev) =>
          prev.currentContent
            ? {
                ...prev,
                currentContent: {
                  ...prev.currentContent,
                  is_business_context: newItemStatus,
                },
              }
            : prev
        );
      }
    } catch (error: any) {
      console.error("Error updating business context:", error);
      let detailedErrorMessage = "Unknown error";
      if (error.response) {
        console.error("API Error Response Data:", error.response.data);
        console.error("API Error Response Status:", error.response.status);
        detailedErrorMessage =
          error.response.data?.error ||
          error.response.data?.message ||
          (typeof error.response.data === "string" &&
            error.response.data.substring(0, 100)) ||
          `Server responded with status ${error.response.status}`;
      } else if (error.request) {
        console.error("API Error Request:", error.request);
        detailedErrorMessage =
          "No response received from server. Check network or CORS settings.";
      } else {
        console.error("API Error Message:", error.message);
        detailedErrorMessage = error.message;
      }
      showNotification(
        `Failed to update business context: ${detailedErrorMessage}`,
        "error"
      );
      setContentItems(originalItems); // Revert optimistic update
      setError(detailedErrorMessage); // Set error state
    } finally {
      setItemActionLoading((prev) => ({ ...prev, [itemId]: false }));
    }
  };

  // Function to delete multiple selected items
  const handleDeleteSelectedItems = async () => {
    if (selectedContentIds.length === 0) return;

    // Directly execute the bulk delete logic since dialog is disabled
    setIsDeletingItems(true);
    setError(null);
    let deletedCount = 0;
    let errorOccurred = false;
    const originalItems = contentItems; // Store for potential rollback
    const itemsToDelete = [...selectedContentIds]; // Copy IDs

    try {
      // Optimistically remove items from UI first
      setContentItems((prevItems) =>
        prevItems.filter((item) => !itemsToDelete.includes(String(item.id)))
      );
      clearContentSelection(); // Clear selection immediately

      for (const id of itemsToDelete) {
        try {
          await api.delete(`/api/content/${id}`);
          deletedCount++;
        } catch (err: any) {
          console.error(`Failed to delete item ${id}:`, err);
          setError(
            (prevError) =>
              `${prevError ? prevError + "; " : ""}Failed to delete item ${id}`
          );
          errorOccurred = true;
          // Add the item back to the UI if its deletion failed
          const failedItem = originalItems.find(
            (item) => String(item.id) === id
          );
          if (failedItem) {
            setContentItems((prevItems) => [...prevItems, failedItem]);
          }
        }
      }

      // Sort items after potential re-additions
      setContentItems((currentItems) =>
        [...currentItems].sort(
          (a, b) =>
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        )
      );

      if (errorOccurred) {
        showNotification(
          `Deleted ${deletedCount} of ${itemsToDelete.length} items. Some errors occurred.`,
          "warning"
        );
      } else {
        showNotification(
          `${deletedCount} content item(s) deleted successfully`,
          "success"
        );
      }

      // Close editor if any of the successfully deleted items were being edited
      if (
        editorState.currentContent &&
        itemsToDelete.includes(editorState.currentContent.id)
      ) {
        const deleteSucceeded =
          !errorOccurred ||
          !itemsToDelete
            .filter((id) => {
              // Check if deletion failed for this specific item
              const errorMsg = error || "";
              return errorMsg.includes(`Failed to delete item ${id}`);
            })
            .includes(editorState.currentContent.id);

        if (deleteSucceeded) {
          handleCancelEdit();
        }
      }
    } catch (err: any) {
      // Catch any unexpected errors during the loop setup or final steps
      setError(
        err.message || "An unexpected error occurred during bulk deletion"
      );
      showNotification(
        `Bulk deletion failed: ${err.message || "Unknown error"}`,
        "error"
      );
      setContentItems(originalItems); // Rollback fully if outer try fails
    } finally {
      setIsDeletingItems(false);
    }
  };

  // Function to add new content
  const handleAddContent = () => {
    setDialogMode("add");
    setCurrentItem(null);
    // Set default content type
    setContentType("blog_post");
    setOpenDialog(true);
  };

  // Function to edit content
  const handleEditContent = (item: ContentItem) => {
    handleEditClick(item); // Use the existing handleEditClick function
  };

  // Function to close notification
  const handleCloseNotification = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  // Function to handle notification close
  const handleNotificationClose = () => {
    setNotification({
      ...notification,
      open: false,
    });
  };

  // Filter content items based on search term and filter type
  const filteredItems = contentItems.filter((item) => {
    const matchesSearch =
      searchTerm === "" ||
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.tags &&
        (Array.isArray(item.tags)
          ? item.tags.some((tag) =>
              tag.toLowerCase().includes(searchTerm.toLowerCase())
            )
          : item.tags.toLowerCase().includes(searchTerm.toLowerCase())));

    const matchesType = filterType === "" || item.content_type === filterType;

    return matchesSearch && matchesType;
  });

  // Task 2.3: Sort filtered items based on sortOption
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortOption) {
      case "title_asc":
        return a.title.localeCompare(b.title);
      case "title_desc":
        return b.title.localeCompare(a.title);
      case "updated_at_asc":
        return (
          new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime()
        );
      case "updated_at_desc":
      default:
        return (
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        );
    }
  });

  // Get unique content types for filter dropdown
  const contentTypes = Array.from(
    new Set(
      contentItems
        .map((item) => item.content_type)
        .filter((type) => type !== null)
    )
  ) as string[];

  // Add autosave function with optimized performance
  useEffect(() => {
    // Only set up autosave when there are unsaved changes and not during active typing
    if (
      !editorState.hasUnsavedChanges ||
      !editorState.isEditing ||
      !editorState.currentContent ||
      formattingLoading ||
      isSavingItem // Don't trigger autosave if we're already saving
    ) {
      return; // Early return to avoid unnecessary timer setup
    }

    // Set up autosave with a longer delay
    const autosaveTimer = setTimeout(() => {
      // Only save when content has actually changed and is not empty
      if (content.trim().length > 0) {
        console.log("Auto-saving content...");
        handleSubmit();
        // Show a non-intrusive notification
        setNotification({
          open: true,
          message: "Content auto-saved",
          severity: "info",
        });
      }
    }, 30000); // 30 seconds for better editing performance

    // Cleanup function
    return () => {
      clearTimeout(autosaveTimer);
    };
  }, [
    editorState.hasUnsavedChanges,
    editorState.isEditing,
    editorState.currentContent,
    formattingLoading,
    isSavingItem,
    content,
    handleSubmit,
  ]);

  // Add navigation protection to prevent losing unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (editorState.hasUnsavedChanges && editorState.isEditing) {
        // Standard way to show a confirmation dialog
        e.preventDefault();
        e.returnValue =
          "You have unsaved changes. Are you sure you want to leave?";
        return e.returnValue;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [editorState.hasUnsavedChanges, editorState.isEditing]);

  // Add keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        if (editorState.hasUnsavedChanges) {
          handleSubmit();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [editorState.hasUnsavedChanges]);

  // Add function to handle canceling edit
  const handleCancelEdit = () => {
    // Check if there are unsaved changes
    if (editorState.hasUnsavedChanges) {
      if (
        !window.confirm(
          "You have unsaved changes. Are you sure you want to cancel?"
        )
      ) {
        return;
      }
    }

    // Reset form state
    resetForm();
  };

  // Add function to toggle preview mode
  const togglePreviewMode = () => {
    setPreviewMode(!previewMode);
  };

  // Toggle markdown preview mode
  const toggleMarkdownPreview = () => {
    setShowMarkdownPreview(!showMarkdownPreview);
  };

  // Toggle selection mode
  const toggleSelectionMode = () => {
    if (selectionMode) {
      // If turning off selection mode, clear selections
      clearContentSelection();
    }
    setSelectionMode(!selectionMode);
  };

  // Create a memoized text editor component for better performance
  const MemoizedTextEditor = React.memo(
    ({
      value,
      onChange,
      placeholder,
      onBlur,
    }: {
      value: string;
      onChange: (value: string) => void;
      placeholder: string;
      onBlur: () => void;
    }) => {
      // Local state for immediate feedback during typing
      const [localValue, setLocalValue] = useState(value);
      // Add state for saving indicator
      const [isSaving, setIsSaving] = useState(false);

      // Update local value when parent value changes
      useEffect(() => {
        // Only update if values are different to prevent unnecessary re-renders
        if (value !== localValue) {
          setLocalValue(value);
        }
      }, [value]);

      // Set up debounced change handler
      const debouncedUpdate = useCallback(
        debounce((newValue: string) => {
          // Only call onChange if the value actually changed
          if (newValue !== value) {
            setIsSaving(true); // Show saving indicator before saving
            onChange(newValue);
            // Hide saving indicator after a short delay to ensure it's visible
            setTimeout(() => setIsSaving(false), 500);
          }
        }, 750), // Reduced to 750ms for more responsive feedback while maintaining performance
        [onChange, value, setIsSaving]
      );

      // Handle changes
      const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        setLocalValue(newValue); // Update local state immediately for responsive typing
        if (newValue !== value) {
          setIsSaving(true); // Show saving indicator immediately when typing
        }
        debouncedUpdate(newValue); // Update parent state after debounce
      };

      // Handle blur - commit changes immediately
      const handleBlur = () => {
        // Only update parent state if value actually changed
        if (localValue !== value) {
          onChange(localValue); // Update parent state immediately
          onBlur();
        }
      };

      return (
        <Box sx={{ position: "relative", width: "100%" }}>
          <TextField
            id="markdown-editor"
            multiline
            fullWidth
            minRows={15}
            maxRows={20}
            value={localValue}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            variant="outlined"
            InputProps={{
              sx: {
                fontFamily: "monospace",
                fontSize: "0.95rem",
                borderRadius: 0,
                "& fieldset": { border: "none" },
              },
            }}
          />
          {isSaving && (
            <Box
              sx={{
                position: "absolute",
                top: "8px",
                right: "8px",
                display: "flex",
                alignItems: "center",
                backgroundColor: "rgba(255,255,255,0.8)",
                padding: "2px 8px",
                borderRadius: "4px",
                gap: "4px",
              }}
            >
              <CircularProgress size={14} />
              <Typography variant="caption" color="text.secondary">
                Saving...
              </Typography>
            </Box>
          )}
        </Box>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if the value actually changed
      return (
        prevProps.value === nextProps.value &&
        prevProps.placeholder === nextProps.placeholder
      );
    }
  );

  // Add state for loading items
  const [loadingItems, setLoadingItems] = useState<Record<string, boolean>>({});

  // Add function to show notification
  const showNotification = (
    message: string,
    severity: "success" | "error" | "info" | "warning"
  ) => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  // Add function to handle menu click
  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    itemId: string
  ) => {
    event.stopPropagation(); // Prevent card click
    setMenuAnchorEl(event.currentTarget);
    setOpenMenuId(itemId);
  };

  // Corrected function signature for MUI Menu onClose
  const handleMenuClose = (
    event?: {},
    reason?: "backdropClick" | "escapeKeyDown"
  ) => {
    // No need for stopPropagation here as it's handled by the Menu component
    // or within MenuItem clicks
    setMenuAnchorEl(null);
    setOpenMenuId(null);
  };

  return (
    <Box sx={{ display: "flex", height: "100%" }}>
      <Box
        sx={{
          flexGrow: 1,
          p: 3,
          display: "flex",
          flexDirection: "column",
          height: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
            Content Library
          </Typography>

          <Box sx={{ display: "flex", gap: 2 }}>
            {selectedContentIds.length > 0 && (
              <Button
                variant="contained"
                color="error"
                startIcon={
                  isDeletingItems ? (
                    <CircularProgress size={18} color="inherit" />
                  ) : (
                    <DeleteIcon />
                  )
                }
                onClick={handleDeleteSelectedItems}
                disabled={isDeletingItems}
                sx={{
                  position: "relative",
                  overflow: "hidden",
                  ...(isDeletingItems && {
                    "&::after": {
                      content: '""',
                      position: "absolute",
                      bottom: 0,
                      left: 0,
                      width: "100%",
                      height: "2px",
                      backgroundColor: "rgba(255, 255, 255, 0.5)",
                      animation: "deleting 2s infinite",
                    },
                    "@keyframes deleting": {
                      "0%": {
                        transform: "translateX(-100%)",
                      },
                      "100%": {
                        transform: "translateX(100%)",
                      },
                    },
                  }),
                }}
              >
                {isDeletingItems
                  ? `Deleting ${selectedContentIds.length} items...`
                  : `Delete Selected (${selectedContentIds.length})`}
              </Button>
            )}

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddContent}
              disabled={isFetchingList || isSavingItem || isDeletingItems} // Disable if any global action is in progress
            >
              Add Content
            </Button>
          </Box>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 1, // Reduced margin bottom slightly
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Grid container spacing={2} alignItems="center">
            {/* Search Input - Adjusted grid size */}
            <Grid item xs={12} md={5}>
              <TextField
                fullWidth
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                variant="outlined"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm("")}
                        edge="end"
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                  sx: { borderRadius: 2 },
                }}
              />
            </Grid>
            {/* Filter Dropdown - Adjusted grid size */}
            <Grid item xs={12} sm={6} md={3.5}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="content-type-filter-label">
                  Content Type
                </InputLabel>
                <Select
                  labelId="content-type-filter-label"
                  id="content-type-filter"
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  label="Content Type"
                  sx={{ borderRadius: 2 }}
                  startAdornment={
                    filterType && (
                      <Box
                        sx={{ display: "flex", alignItems: "center", mr: 1 }}
                      >
                        <ListIcon fontSize="small" sx={{ mr: 0.5 }} />
                        <Typography
                          variant="body2"
                          color="primary"
                          sx={{ fontWeight: 500 }}
                        >
                          Filtering:
                        </Typography>
                      </Box>
                    )
                  }
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="blog_post">Blog Posts</MenuItem>
                  <MenuItem value="product_description">
                    Product Descriptions
                  </MenuItem>
                  <MenuItem value="landing_page">Landing Pages</MenuItem>
                  <MenuItem value="youtube_video">YouTube Videos</MenuItem>
                  <MenuItem value="draft">Drafts</MenuItem>
                  <MenuItem value="email">Emails</MenuItem>
                  <MenuItem value="social_media">Social Media</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {/* Task 2.3: Sort Dropdown - Added grid item */}
            <Grid item xs={12} sm={6} md={3.5}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="sort-by-label">Sort By</InputLabel>
                <Select
                  labelId="sort-by-label"
                  value={sortOption}
                  onChange={(e) => setSortOption(e.target.value)}
                  label="Sort By"
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="updated_at_desc">
                    Date Updated (Newest)
                  </MenuItem>
                  <MenuItem value="updated_at_asc">
                    Date Updated (Oldest)
                  </MenuItem>
                  <MenuItem value="title_asc">Title (A-Z)</MenuItem>
                  <MenuItem value="title_desc">Title (Z-A)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>

        {/* Task 2.4: Active Filter Display */}
        {(searchTerm || filterType) && (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 3, px: 1 }}>
            <Typography
              variant="body2"
              sx={{ alignSelf: "center", mr: 1, color: "text.secondary" }}
            >
              Active Filters:
            </Typography>
            {searchTerm && (
              <Chip
                label={`Search: "${searchTerm}"`}
                onClick={() => setSearchTerm("")}
                onDelete={() => setSearchTerm("")}
                size="small"
              />
            )}
            {filterType && (
              <Chip
                label={`Type: ${filterType.replace("_", " ")}`}
                onClick={() => setFilterType("")}
                onDelete={() => setFilterType("")}
                size="small"
                sx={{ textTransform: "capitalize" }}
              />
            )}
          </Box>
        )}

        <Box sx={{ flexGrow: 1, overflowY: "auto", mb: 2 }}>
          {isFetchingList ? (
            <Grid container spacing={3}>
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <Grid item xs={12} sm={6} md={4} key={`skeleton-${item}`}>
                  <Card
                    sx={{
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      borderRadius: 2,
                      border: "1px solid",
                      borderColor: "divider",
                    }}
                  >
                    <Skeleton
                      variant="rectangular"
                      height={160}
                      animation="wave"
                      sx={{ borderTopLeftRadius: 8, borderTopRightRadius: 8 }}
                    />
                    <CardContent sx={{ pb: 1, flexGrow: 1 }}>
                      <Skeleton animation="wave" height={32} width="80%" />
                      <Box sx={{ mt: 1, display: "flex", gap: 1 }}>
                        <Skeleton animation="wave" height={24} width={60} />
                        <Skeleton animation="wave" height={24} width={40} />
                        <Skeleton animation="wave" height={24} width={50} />
                      </Box>
                      <Box sx={{ mt: 2, minHeight: "20px" }}>
                        <Skeleton animation="wave" height={20} width="60%" />
                      </Box>
                    </CardContent>
                    <Box
                      sx={{
                        mt: "auto",
                        borderTop: "1px solid",
                        borderColor: "divider",
                        px: 2,
                        py: 1,
                      }}
                    >
                      <Skeleton animation="wave" height={20} width="40%" />
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : error ? (
            <Paper
              elevation={0}
              sx={{
                p: 3,
                textAlign: "center",
                borderRadius: 2,
                border: "1px solid",
                borderColor: "error.light",
                backgroundColor: "rgba(211, 47, 47, 0.04)", // Lighter error background
                color: "text.primary",
                maxWidth: "800px",
                mx: "auto",
                mt: 4,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 2,
                }}
              >
                <WarningIcon sx={{ fontSize: 48, color: "error.main" }} />
                <Typography variant="h6" gutterBottom fontWeight={500}>
                  Unable to Load Content
                </Typography>
                <Typography variant="body1" sx={{ maxWidth: "600px", mb: 1 }}>
                  We encountered a problem while loading your content. This
                  could be due to a network issue or server unavailability.
                </Typography>
                <Typography
                  variant="body2"
                  color="error"
                  sx={{
                    mb: 2,
                    fontFamily: "monospace",
                    p: 2,
                    bgcolor: "rgba(0,0,0,0.04)",
                    borderRadius: 1,
                    maxWidth: "100%",
                    overflow: "auto",
                  }}
                >
                  {getErrorMessage(error)}
                </Typography>
                <Box sx={{ display: "flex", gap: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => fetchContentItems()}
                    startIcon={<SearchIcon />}
                  >
                    Try Again
                  </Button>
                  <Button
                    variant="outlined"
                    color="inherit"
                    onClick={() => setError(null)}
                  >
                    Dismiss
                  </Button>
                </Box>
              </Box>
            </Paper>
          ) : contentItems.length === 0 && !searchTerm && !filterType ? ( // Adjusted condition for empty state
            <Paper
              elevation={0}
              sx={{
                p: 4,
                textAlign: "center",
                borderRadius: 2,
                border: "1px solid",
                borderColor: "divider",
              }}
            >
              <LibraryBooksIcon
                sx={{ fontSize: 60, mb: 2, color: "text.secondary" }}
              />
              <Typography variant="h6" gutterBottom>
                No Content Found
              </Typography>
              <Typography color="textSecondary" paragraph>
                {searchTerm || filterType
                  ? "No content matches your search criteria. Try adjusting your search or filters."
                  : "Your content library is empty. Start by adding your first content item."}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleAddContent}
                sx={{ mt: 2 }}
                disabled={isFetchingList || isSavingItem || isDeletingItems} // Disable if any global action is in progress
              >
                Add Content
              </Button>
            </Paper>
          ) : sortedItems.length === 0 ? ( // Use sortedItems here
            <Paper
              elevation={0}
              sx={{
                p: 4,
                textAlign: "center",
                borderRadius: 2,
                border: "1px solid",
                borderColor: "divider",
              }}
            >
              <SearchIcon
                sx={{ fontSize: 60, mb: 2, color: "text.secondary" }}
              />
              <Typography variant="h6" gutterBottom>
                No Content Matches Filters
              </Typography>
              <Typography color="textSecondary" paragraph>
                Try adjusting your search term or selected content type.
              </Typography>
              <Button
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setFilterType("");
                }}
                sx={{ mt: 2 }}
              >
                Clear Filters
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {/* Use sortedItems for mapping */}
              {sortedItems.map((item, index) => (
                <Grid item xs={12} sm={6} md={4} key={item.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.05,
                      ease: "easeOut",
                    }}
                    layout
                  >
                    <Card
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        borderRadius: 2,
                        transition: "transform 0.2s, box-shadow 0.2s",
                        "&:hover": {
                          transform: "translateY(-4px)",
                          boxShadow: 4,
                        },
                        border: "1px solid",
                        borderColor: "divider",
                        position: "relative",
                        ...(isContentSelected(String(item.id)) && {
                          border: "2px solid",
                          borderColor: "primary.main",
                          boxShadow: 4,
                        }),
                        opacity: itemActionLoading[item.id] ? 0.7 : 1, // Dim card if single item action is loading
                      }}
                    >
                      {/* Checkbox top-left */}
                      <Checkbox
                        checked={isContentSelected(String(item.id))}
                        color="primary"
                        sx={{
                          position: "absolute",
                          top: 8,
                          left: 8, // Changed from right to left
                          zIndex: 1,
                          // Removed background for cleaner look on top-left
                          padding: "4px",
                          // '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' } // Optional subtle hover
                        }}
                        onClick={(e) => {
                          // Keep stopPropagation if card click is removed/changed
                          e.stopPropagation();
                          toggleContentSelection(String(item.id));
                        }}
                      />

                      {/* 3-dots Menu top-right */}
                      <Box
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          zIndex: 1,
                        }}
                      >
                        <Tooltip title="More options">
                          {/* Wrap IconButton in span for Tooltip when disabled */}
                          <span>
                            <IconButton
                              aria-label="more options"
                              id={`long-button-${item.id}`}
                              aria-controls={
                                openMenuId === item.id
                                  ? `long-menu-${item.id}`
                                  : undefined
                              }
                              aria-expanded={
                                openMenuId === item.id ? "true" : undefined
                              }
                              aria-haspopup="true"
                              onClick={(e) => handleMenuClick(e, item.id)}
                              size="small"
                              disabled={itemActionLoading[item.id]} // Disable based on item loading state
                              sx={{
                                p: 0.5,
                                opacity: itemActionLoading[item.id] ? 0.5 : 1,
                                bgcolor: "rgba(255, 255, 255, 0.7)", // Add slight background for visibility
                                "&:hover": {
                                  bgcolor: "rgba(255, 255, 255, 0.9)",
                                },
                              }}
                            >
                              <MoreVertIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>

                      {/* --- END MOVED CONTROLS --- */}

                      {/* Thumbnail/Placeholder Area (No changes needed here based on request) */}
                      {(() => {
                        // Determine the image URL to use
                        let imageUrl = item.thumbnail_url; // Prioritize direct URL from DB

                        // If no direct URL and it's a YouTube video, try extracting from content
                        if (
                          !imageUrl &&
                          item.content_type === "youtube_video" &&
                          typeof item.content === "string"
                        ) {
                          // Define or ensure extractYouTubeThumbnail is available here
                          // Assuming extractYouTubeThumbnail function exists in this scope
                          const extractYouTubeThumbnail = (
                            content: string
                          ): string => {
                            const thumbnailMatch = content.match(
                              /Thumbnail:\s*(https?:\/\/[^\s\n]+)/
                            );
                            // Basic check for common image extensions or YouTube domain
                            if (
                              thumbnailMatch &&
                              thumbnailMatch[1] &&
                              (thumbnailMatch[1].includes("ytimg.com") ||
                                /\.(jpg|jpeg|png|webp)/i.test(
                                  thumbnailMatch[1]
                                ))
                            ) {
                              return thumbnailMatch[1].trim();
                            }
                            // Add fallback for direct YouTube URL extraction if needed
                            const youtubeUrlMatch = content.match(
                              /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/
                            );
                            if (youtubeUrlMatch && youtubeUrlMatch[1]) {
                              return `https://i.ytimg.com/vi/${youtubeUrlMatch[1]}/hqdefault.jpg`;
                            }
                            return ""; // Return empty string if no valid URL found
                          };
                          imageUrl = extractYouTubeThumbnail(item.content);
                        }

                        // Now render based on whether we found a valid imageUrl
                        if (imageUrl) {
                          return (
                            <Box
                              sx={{
                                height: 160,
                                overflow: "hidden",
                                borderTopLeftRadius: 8,
                                borderTopRightRadius: 8,
                                position: "relative",
                                bgcolor: "grey.200", // Background for loading/error states
                              }}
                            >
                              <img
                                src={imageUrl}
                                alt={item.title}
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                }}
                                // Simple onError: hide the broken image element
                                onError={(e) => {
                                  e.currentTarget.style.display = "none";
                                  // Optionally: Could set a state to show placeholder here if needed
                                }}
                              />
                            </Box>
                          );
                        } else {
                          // Render placeholder if no imageUrl could be determined
                          return (
                            <Box
                              sx={{
                                height: 160,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                bgcolor: "#546e7a", // Changed to a Docker-like blue-grey
                                color: "common.white", // Changed icon color to white for contrast
                                borderTopLeftRadius: 8,
                                borderTopRightRadius: 8,
                              }}
                            >
                              {item.content_type === "youtube_video" ? (
                                <YouTubeIcon sx={{ fontSize: 60 }} />
                              ) : (
                                <LibraryBooksIcon sx={{ fontSize: 60 }} />
                              )}
                            </Box>
                          );
                        }
                      })()}

                      {/* --- REPLACING CARD CONTENT, FOOTER, and MENU STRUCTURE --- */}
                      <CardContent
                        sx={{ pb: 1, flexGrow: 1 /* Allow content to grow */ }}
                      >
                        {/* Title and Business Context Icon */}
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            mb: 1,
                          }}
                        >
                          <Typography
                            gutterBottom
                            variant="h6"
                            component="div"
                            sx={{
                              fontWeight: 500,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: "2",
                              WebkitBoxOrient: "vertical",
                              mr: 1,
                            }}
                          >
                            {item.title}
                          </Typography>
                          <Tooltip
                            title={
                              itemActionLoading[item.id]
                                ? "Updating status..."
                                : item.is_business_context
                                ? "Marked as Business Context"
                                : "Not Business Context (Click to toggle)"
                            }
                            arrow
                          >
                            <span>
                              {" "}
                              {/* Span needed for tooltip when button is disabled */}
                              <IconButton
                                size="small"
                                sx={{
                                  p: 0.5,
                                  color: item.is_business_context
                                    ? "primary.main"
                                    : "action.disabled",
                                  opacity: itemActionLoading[item.id] ? 0.5 : 1,
                                  cursor: itemActionLoading[item.id]
                                    ? "progress"
                                    : "pointer",
                                  transition: "all 0.2s",
                                  position: "relative",
                                  ...(itemActionLoading[item.id] && {
                                    "&::after": {
                                      content: '""',
                                      position: "absolute",
                                      top: 0,
                                      left: 0,
                                      right: 0,
                                      bottom: 0,
                                      borderRadius: "50%",
                                      border: "2px solid",
                                      borderColor: "primary.light",
                                      borderTopColor: "transparent",
                                      animation: "spin 1s linear infinite",
                                    },
                                    "@keyframes spin": {
                                      "0%": {
                                        transform: "rotate(0deg)",
                                      },
                                      "100%": {
                                        transform: "rotate(360deg)",
                                      },
                                    },
                                  }),
                                }}
                                disabled={itemActionLoading[item.id]}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (!itemActionLoading[item.id]) {
                                    handleToggleBusinessContext(
                                      item.id,
                                      !!item.is_business_context
                                    );
                                  }
                                }}
                              >
                                <BusinessIcon fontSize="small" />
                              </IconButton>
                            </span>
                          </Tooltip>
                        </Box>

                        {/* --- IMPROVED TAGS DISPLAY --- */}
                        {(() => {
                          // Clean up tags using our standardizeTags helper function
                          const tagsToRender = standardizeTags(item.tags);
                          const totalTags = tagsToRender.length;
                          const tagsToShow = 3; // Max tags to show directly

                          return totalTags > 0 ? (
                            <Box
                              sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: 0.5,
                                mt: 1,
                                minHeight:
                                  "26px" /* Ensure space even if no tags */,
                              }}
                            >
                              {tagsToRender
                                .slice(0, tagsToShow)
                                .map((tag, index) => (
                                  <Chip
                                    key={index}
                                    label={tag}
                                    size="small"
                                    variant="outlined"
                                    sx={{
                                      fontSize: "0.7rem",
                                      textTransform: "capitalize",
                                      cursor: "pointer",
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation(); // Prevent card click
                                      setSearchTerm(tag); // Allow clicking tag to filter
                                    }}
                                  />
                                ))}

                              {totalTags > tagsToShow && (
                                <Chip
                                  label={`+${totalTags - tagsToShow}`}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: "0.7rem" }}
                                />
                              )}
                            </Box>
                          ) : (
                            <Box
                              sx={{ minHeight: "26px" }}
                            /> /* Placeholder height if no tags */
                          );
                        })()}
                        {/* --- END TAGS DISPLAY --- */}

                        {/* --- ADDED CHANNEL/STATS PLACEHOLDERS --- */}
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1.5,
                            mt: 1.5,
                            minHeight: "20px",
                          }}
                        >
                          {item.channel && (
                            <Tooltip title={`Channel: ${item.channel}`}>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 0.5,
                                }}
                              >
                                <YouTubeIcon fontSize="small" color="action" />
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  noWrap
                                  sx={{ maxWidth: "100px" }}
                                >
                                  {item.channel}
                                </Typography>
                              </Box>
                            </Tooltip>
                          )}
                          {item.stats?.views !== undefined && (
                            <Tooltip
                              title={`Views: ${item.stats.views.toLocaleString()}`}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 0.5,
                                }}
                              >
                                <VisibilityIcon
                                  fontSize="small"
                                  color="action"
                                />
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                >
                                  {/* Simple formatter for large numbers */}
                                  {item.stats.views > 9999
                                    ? `${(item.stats.views / 1000).toFixed(0)}k`
                                    : item.stats.views}
                                </Typography>
                              </Box>
                            </Tooltip>
                          )}
                          {/* Add Likes/Comments similarly if needed */}
                        </Box>
                        {/* --- END CHANNEL/STATS --- */}
                      </CardContent>
                      {/* CardContent ends here */}

                      {/* Footer - Positioned after CardContent, uses mt: auto to push down */}
                      <Box
                        sx={{
                          mt: "auto",
                          borderTop: "1px solid",
                          borderColor: "divider",
                        }}
                      >
                        <Box
                          sx={{
                            px: 2,
                            py: 1,
                            display: "flex",
                            justifyContent: "flex-start", // Align date to the start
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{
                              fontSize: "0.75rem",
                              whiteSpace: "nowrap",
                            }}
                          >
                            Updated:{" "}
                            {new Date(item.updated_at).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Box>
                      {/* Footer Box ends here */}

                      {/* --- Menu component remains a direct child of Card for positioning --- */}
                      {/* Ensure it's outside CardContent and Footer Box */}
                      <Menu
                        id={`long-menu-${item.id}`}
                        MenuListProps={{
                          "aria-labelledby": `long-button-${item.id}`,
                        }}
                        anchorEl={menuAnchorEl}
                        open={openMenuId === item.id}
                        onClose={handleMenuClose} // Use existing close handler
                        anchorOrigin={{
                          vertical: "bottom",
                          horizontal: "right",
                        }}
                        transformOrigin={{
                          vertical: "top",
                          horizontal: "right",
                        }}
                        PaperProps={{
                          elevation: 3,
                        }}
                      >
                        {/* --- Calculate videoId before rendering MenuItems --- */}
                        {(() => {
                          let videoId: string | null = null;
                          if (item.content_type === "youtube_video") {
                            const videoIdMatch =
                              typeof item.thumbnail_url === "string"
                                ? item.thumbnail_url.match(
                                    /vi\/([^/]+)\/.*\.jpg/
                                  )
                                : null;
                            const videoIdFromContent =
                              typeof item.content === "string"
                                ? item.content.match(
                                    /(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/
                                  )
                                : null;
                            videoId = videoIdMatch
                              ? videoIdMatch[1]
                              : videoIdFromContent
                              ? videoIdFromContent[1]
                              : null;
                          }
                          // Render the MenuItems using the calculated videoId
                          return (
                            <>
                              <MenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditContent(item);
                                  handleMenuClose();
                                }}
                              >
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText>Edit</ListItemText>
                              </MenuItem>
                              <MenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleBusinessContext(
                                    item.id,
                                    !!item.is_business_context
                                  );
                                  handleMenuClose();
                                }}
                                disabled={itemActionLoading[item.id]}
                              >
                                <ListItemIcon>
                                  <Checkbox
                                    edge="start"
                                    checked={!!item.is_business_context}
                                    tabIndex={-1}
                                    disableRipple
                                    size="small"
                                    sx={{ p: 0, mr: 0.5 }}
                                  />
                                </ListItemIcon>
                                <ListItemText>
                                  {item.is_business_context
                                    ? "Unmark Business"
                                    : "Mark Business"}
                                </ListItemText>
                              </MenuItem>
                              <Divider sx={{ my: 0.5 }} />
                              <MenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteContent(item.id);
                                  handleMenuClose();
                                }}
                                sx={{ color: "error.main" }}
                                disabled={itemActionLoading[item.id]}
                              >
                                <ListItemIcon>
                                  <DeleteIcon
                                    fontSize="small"
                                    sx={{ color: "error.main" }}
                                  />
                                </ListItemIcon>
                                <ListItemText>Delete</ListItemText>
                              </MenuItem>
                              {/* Use simplified conditional rendering with pre-calculated videoId */}
                              {item.content_type === "youtube_video" &&
                                videoId && (
                                  <MenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      window.open(
                                        `https://youtube.com/watch?v=${videoId}`,
                                        "_blank"
                                      );
                                      handleMenuClose();
                                    }}
                                  >
                                    <ListItemIcon>
                                      <YouTubeIcon
                                        fontSize="small"
                                        color="error"
                                      />
                                    </ListItemIcon>
                                    <ListItemText>
                                      Watch on YouTube
                                    </ListItemText>
                                  </MenuItem>
                                )}
                            </>
                          );
                        })()}
                      </Menu>
                      {/* Menu ends here */}
                      {/* --- END REPLACEMENT BLOCK --- */}
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      </Box>

      <Dialog
        open={openDialog}
        fullWidth
        maxWidth="md"
        onClose={resetForm}
        sx={{
          "& .MuiDialog-paper": {
            minHeight: "80vh",
            maxHeight: "90vh",
            borderRadius: 2,
          },
        }}
      >
        <AppBar
          position="relative"
          color="primary"
          sx={{ borderTopLeftRadius: 8, borderTopRightRadius: 8 }}
        >
          <Toolbar>
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 500 }}>
              {dialogMode === "add" ? "Add New Content" : "Edit Content"}
            </Typography>
            <Box>
              {contentType === "youtube_video" && (
                <Button
                  color="inherit"
                  startIcon={
                    formattingLoading ? (
                      <CircularProgress size={16} color="inherit" />
                    ) : (
                      <AutoAwesomeIcon />
                    )
                  }
                  onClick={handleFormatTranscript}
                  disabled={formattingLoading}
                  variant={formattingLoading ? "outlined" : "text"}
                  sx={{
                    mr: 1,
                    position: "relative",
                    overflow: "hidden",
                    ...(formattingLoading && {
                      "&::after": {
                        content: '""',
                        position: "absolute",
                        bottom: 0,
                        left: 0,
                        width: "100%",
                        height: "2px",
                        backgroundColor: "primary.light",
                        animation: "loading 1.5s infinite",
                      },
                      "@keyframes loading": {
                        "0%": {
                          transform: "translateX(-100%)",
                        },
                        "50%": {
                          transform: "translateX(0)",
                        },
                        "100%": {
                          transform: "translateX(100%)",
                        },
                      },
                    }),
                  }}
                >
                  {formattingLoading ? "Formatting..." : "Format with AI"}
                </Button>
              )}
              <IconButton
                edge="end"
                color="inherit"
                onClick={resetForm}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Toolbar>
        </AppBar>

        <DialogContent sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                label="Title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  setEditorState((prev) => ({
                    ...prev,
                    hasUnsavedChanges: true,
                  }));
                }}
                fullWidth
                variant="outlined"
                required
                sx={{ mb: 3 }}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: 2 }}>
                <Tabs
                  value={showMarkdownPreview ? 1 : 0}
                  onChange={(_, newValue) =>
                    setShowMarkdownPreview(Boolean(newValue))
                  }
                  aria-label="editor-preview-tabs"
                  variant="fullWidth"
                  sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
                >
                  <Tab
                    icon={<EditIcon />}
                    label="Edit"
                    id="tab-edit"
                    aria-controls="tabpanel-edit"
                  />
                  <Tab
                    icon={<PreviewIcon />}
                    label="Preview"
                    id="tab-preview"
                    aria-controls="tabpanel-preview"
                  />
                </Tabs>
              </Box>

              {!showMarkdownPreview ? (
                <Box
                  role="tabpanel"
                  id="tabpanel-edit"
                  aria-labelledby="tab-edit"
                  sx={{
                    border: "1px solid",
                    borderColor: "divider",
                    borderRadius: 1,
                  }}
                >
                  <EditorToolbar onFormat={handleFormat} />
                  <MemoizedTextEditor
                    value={content}
                    onChange={(value) => {
                      // Use our safe wrapper to update content
                      updateContentSafely(value);
                    }}
                    placeholder={
                      contentType === "youtube_video"
                        ? "Paste YouTube transcript here..."
                        : "Enter your content here using Markdown syntax..."
                    }
                    onBlur={() => {
                      // Force update on blur to ensure content is saved
                      setEditorState((prev) => ({
                        ...prev,
                        hasUnsavedChanges: true,
                      }));
                    }}
                  />
                </Box>
              ) : (
                <Box
                  role="tabpanel"
                  id="tabpanel-preview"
                  aria-labelledby="tab-preview"
                  sx={{
                    border: "1px solid",
                    borderColor: "divider",
                    borderRadius: 1,
                    p: 3,
                    minHeight: "400px",
                    maxHeight: "600px",
                    overflow: "auto",
                    bgcolor: "background.paper",
                  }}
                >
                  {content ? (
                    <MarkdownRenderer content={content} />
                  ) : (
                    <Typography
                      color="text.secondary"
                      sx={{ fontStyle: "italic" }}
                    >
                      No content to preview
                    </Typography>
                  )}
                </Box>
              )}

              {contentType === "blog_post" && !showMarkdownPreview && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ display: "block", mt: 1 }}
                >
                  <InfoIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  You can use Markdown syntax to format your content. Click the
                  "Preview" tab to see how it will be displayed.
                </Typography>
              )}
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions
          sx={{ px: 3, py: 2, borderTop: "1px solid", borderColor: "divider" }}
        >
          <Button
            onClick={resetForm}
            color="inherit"
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            disabled={
              !title.trim() ||
              !content.trim() ||
              formattingLoading ||
              isSavingItem
            }
            startIcon={
              isSavingItem ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SaveIcon />
              )
            }
          >
            {isSavingItem ? "Saving..." : "Save"}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={notification.open}
        autoHideDuration={notification.severity === "success" ? 4000 : 6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        sx={{
          bottom: { xs: 16, sm: 24 },
          "& .MuiAlert-root": {
            width: "100%",
            boxShadow: 3,
            alignItems: "center",
          },
        }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: "100%",
            maxWidth: "440px",
            display: "flex",
            alignItems: "center",
            "& .MuiAlert-icon": {
              fontSize: "1.25rem",
              marginRight: 1,
            },
            "& .MuiAlert-message": {
              fontSize: "0.9rem",
              fontWeight: notification.severity === "error" ? 500 : 400,
            },
            "& .MuiAlert-action": {
              paddingTop: 0,
            },
          }}
          iconMapping={{
            success: <CheckIcon fontSize="inherit" />,
            error: <ErrorIcon fontSize="inherit" />,
            warning: <WarningIcon fontSize="inherit" />,
            info: <InfoIcon fontSize="inherit" />,
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ContentLibrary;
