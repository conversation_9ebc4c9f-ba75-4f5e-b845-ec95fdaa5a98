# Writer v2 API Documentation

This document provides detailed information about the Writer v2 API endpoints, request/response formats, and authentication requirements.

## API Route Standardization - Business Goals & Constraints

### Business Goals
- **Zero Breaking Changes**: Ensure no disruption to existing deployed clients during the route standardization process
- **Consistent API Structure**: Establish a unified `/api` prefix pattern across all endpoints to eliminate confusion and duplication
- **Developer Experience**: Simplify API consumption by having a single, predictable URL structure
- **Maintainability**: Reduce code complexity by centralizing route prefix management

### Technical Constraints & Decisions
- **Route Prefix Strategy**: Base URL includes `/api` prefix; individual endpoint paths do NOT start with `/api`
  - ✅ Correct: `baseURL: "http://localhost:5000/api"` + endpoint: `"/chat/sessions"`
  - ❌ Incorrect: `baseURL: "http://localhost:5000"` + endpoint: `"/api/chat/sessions"`
- **Backward Compatibility**: All existing API endpoints maintain their functionality during transition
- **Environment Configuration**: `REACT_APP_API_URL` environment variable must include the `/api` prefix
- **Streaming Endpoints**: Real-time endpoints (like `/chat/stream`) follow the same prefix pattern

### Implementation Status
- ✅ **Hot-fix Applied**: Immediate fixes implemented to resolve 404 errors
- ✅ **Frontend Standardization**: All frontend API calls updated to use consistent base URL pattern
- ⏳ **Testing & Validation**: Comprehensive testing required to ensure no regressions
- ⏳ **Documentation Updates**: API documentation updated to reflect standardized patterns

### Breaking Change Prevention
- All changes maintain existing API contract
- Environment variables provide flexibility for different deployment scenarios
- Docker and local development configurations preserved
- No changes to actual backend endpoint definitions required

## Authentication API

The authentication API handles user registration, login, and profile management.

### Base URL

All authentication endpoints are prefixed with `/api/auth`.

### Endpoints

#### Register User

```
POST /api/auth/register
```

**Description:** Register a new user account and return an access token.

**Request Body:**

| Field      | Type   | Required | Description                           |
|------------|--------|----------|---------------------------------------|
| username   | string | Yes      | Unique username (3-64 characters)     |
| email      | string | Yes      | Valid email address                   |
| password   | string | Yes      | Password (min 8 characters)           |
| first_name | string | No       | User's first name                     |
| last_name  | string | No       | User's last name                      |

**Response:**

- Status: 201 Created

```json
{
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "username": "example_user",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:00:00Z",
    "is_active": true
  },
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Error Responses:**

- 400 Bad Request: Missing required fields
- 409 Conflict: Username or email already exists
- 500 Internal Server Error: Registration failed

#### User Login

```
POST /api/auth/login
```

**Description:** Authenticate a user and return an access token.

**Request Body:**

| Field    | Type   | Required | Description       |
|----------|--------|----------|-------------------|
| username | string | Yes      | User's username   |
| password | string | Yes      | User's password   |

**Response:**

- Status: 200 OK

```json
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "username": "example_user",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:00:00Z",
    "is_active": true
  },
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Error Responses:**

- 400 Bad Request: Missing username or password
- 401 Unauthorized: Invalid username or password
- 403 Forbidden: Account is deactivated

#### Get User Profile

```
GET /api/auth/profile
```

**Description:** Get the current user's profile information.

**Authentication:** JWT token required in Authorization header.

**Response:**

- Status: 200 OK

```json
{
  "id": 1,
  "username": "example_user",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z",
  "is_active": true
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 404 Not Found: User not found

#### Update User Profile

```
PUT /api/auth/profile
```

**Description:** Update the current user's profile information.

**Authentication:** JWT token required in Authorization header.

**Request Body:**

| Field      | Type   | Required | Description                 |
|------------|--------|----------|-----------------------------|  
| first_name | string | No       | User's first name           |
| last_name  | string | No       | User's last name            |
| email      | string | No       | User's email address        |
| password   | string | No       | New password                |

**Response:**

- Status: 200 OK

```json
{
  "message": "Profile updated successfully",
  "user": {
    "id": 1,
    "username": "example_user",
    "email": "<EMAIL>",
    "first_name": "Updated",
    "last_name": "Name",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-02T12:00:00Z",
    "is_active": true
  }
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 404 Not Found: User not found
- 409 Conflict: Email already exists
- 500 Internal Server Error: Profile update failed

## Business Context API

The business context API handles management of business information including offer descriptions, target audience, brand voice, and value propositions.

### Base URL

All business context endpoints are prefixed with `/api/business`.

### Endpoints

#### Create Business Context

```
POST /api/business/
```

**Description:** Create a new business context entry.

**Authentication:** JWT token required in Authorization header.

**Request Body:**

| Field                   | Type   | Required | Description                                  |
|-------------------------|--------|----------|----------------------------------------------|
| offer_description       | string | Yes      | Description of the business offer/product    |
| target_audience         | string | Yes      | Description of the target audience/customers |
| brand_voice             | string | No       | Brand's tone and communication style         |
| key_benefits            | string | No       | Key benefits of the product/service         |
| unique_value_proposition| string | No       | Unique selling proposition                   |

**Response:**

- Status: 201 Created

```json
{
  "message": "Business context created successfully",
  "context": {
    "id": 1,
    "user_id": 1,
    "offer_description": "Premium content writing service for tech companies",
    "target_audience": "B2B SaaS companies with 10-100 employees",
    "brand_voice": "Professional, authoritative, but approachable",
    "key_benefits": "Increased conversion rates, consistent messaging, expert industry knowledge",
    "unique_value_proposition": "AI-powered content that maintains your brand voice while driving conversions",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:00:00Z"
  }
}
```

**Error Responses:**

- 400 Bad Request: Missing required fields
- 401 Unauthorized: Missing or invalid token
- 500 Internal Server Error: Failed to create business context

#### Get All Business Contexts

```
GET /api/business/
```

**Description:** Get all business contexts for the current user.

**Authentication:** JWT token required in Authorization header.

**Response:**

- Status: 200 OK

```json
{
  "contexts": [
    {
      "id": 1,
      "user_id": 1,
      "offer_description": "Premium content writing service for tech companies",
      "target_audience": "B2B SaaS companies with 10-100 employees",
      "brand_voice": "Professional, authoritative, but approachable",
      "key_benefits": "Increased conversion rates, consistent messaging, expert industry knowledge",
      "unique_value_proposition": "AI-powered content that maintains your brand voice while driving conversions",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    {
      "id": 2,
      "user_id": 1,
      "offer_description": "Social media management for small businesses",
      "target_audience": "Local retail businesses with limited marketing resources",
      "brand_voice": "Friendly, helpful, community-oriented",
      "key_benefits": "Increased local engagement, consistent posting schedule, trend awareness",
      "unique_value_proposition": "Locally-focused social media that drives foot traffic",
      "created_at": "2023-01-02T12:00:00Z",
      "updated_at": "2023-01-02T12:00:00Z"
    }
  ]
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 500 Internal Server Error: Failed to fetch business contexts

#### Get Specific Business Context

```
GET /api/business/<context_id>
```

**Description:** Get a specific business context by ID.

**Authentication:** JWT token required in Authorization header.

**Response:**

- Status: 200 OK

```json
{
  "id": 1,
  "user_id": 1,
  "offer_description": "Premium content writing service for tech companies",
  "target_audience": "B2B SaaS companies with 10-100 employees",
  "brand_voice": "Professional, authoritative, but approachable",
  "key_benefits": "Increased conversion rates, consistent messaging, expert industry knowledge",
  "unique_value_proposition": "AI-powered content that maintains your brand voice while driving conversions",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 404 Not Found: Business context not found

#### Update Business Context

```
PUT /api/business/<context_id>
```

**Description:** Update a specific business context.

**Authentication:** JWT token required in Authorization header.

**Request Body:**

| Field                   | Type   | Required | Description                                  |
|-------------------------|--------|----------|----------------------------------------------|
| offer_description       | string | No       | Description of the business offer/product    |
| target_audience         | string | No       | Description of the target audience/customers |
| brand_voice             | string | No       | Brand's tone and communication style         |
| key_benefits            | string | No       | Key benefits of the product/service         |
| unique_value_proposition| string | No       | Unique selling proposition                   |

**Response:**

- Status: 200 OK

```json
{
  "message": "Business context updated successfully",
  "context": {
    "id": 1,
    "user_id": 1,
    "offer_description": "Premium content writing service for tech companies",
    "target_audience": "Updated target audience: B2B SaaS companies with 50-500 employees",
    "brand_voice": "Professional, authoritative, but approachable",
    "key_benefits": "Increased conversion rates, consistent messaging, expert industry knowledge",
    "unique_value_proposition": "Updated value prop: Enterprise-grade content that maintains your brand voice",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-03T15:30:00Z"
  }
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 404 Not Found: Business context not found
- 500 Internal Server Error: Failed to update business context

#### Delete Business Context

```
DELETE /api/business/<context_id>
```

**Description:** Delete a specific business context.

**Authentication:** JWT token required in Authorization header.

**Response:**

- Status: 200 OK

```json
{
  "message": "Business context deleted successfully"
}
```

**Error Responses:**

- 401 Unauthorized: Missing or invalid token
- 404 Not Found: Business context not found
- 500 Internal Server Error: Failed to delete business context

## Authentication

Most API endpoints require authentication using JSON Web Tokens (JWT).

### JWT Authentication

To authenticate requests, include the JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

The token is obtained from the login or registration endpoints.

### Token Expiration

Access tokens expire after the configured time period (default: 1 hour). When a token expires, the client must request a new token by authenticating again.