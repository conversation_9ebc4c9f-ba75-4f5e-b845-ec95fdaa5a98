name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  security-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Fetch full history for comprehensive secret scanning

    - name: Secret Detection
      run: |
        echo "🔍 Scanning for exposed secrets..."

        # Define patterns for different types of secrets
        OPENROUTER_PATTERN="sk-or-v1-[a-zA-Z0-9]{64,}"
        OPENAI_PATTERN="sk-[a-zA-Z0-9]{48,}"
        JWT_PATTERN="[A-Za-z0-9_-]{100,}\.[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}"
        SUPABASE_KEY_PATTERN="eyJ[A-Za-z0-9_-]{100,}"
        YOUTUBE_KEY_PATTERN="AIza[A-Za-z0-9_-]{35}"
        GENERIC_SECRET_PATTERN="(secret|key|token|password).*[=:]\s*['\"][a-zA-Z0-9_-]{20,}['\"]"

        # Function to check for secrets in files
        check_secrets() {
          local file="$1"
          local found_secrets=0

          # Skip binary files and node_modules
          if file "$file" | grep -q "binary\|executable" || [[ "$file" == *"node_modules"* ]] || [[ "$file" == *".git"* ]]; then
            return 0
          fi

          # Check for OpenRouter API keys
          if grep -qE "$OPENROUTER_PATTERN" "$file"; then
            echo "❌ SECURITY VIOLATION: OpenRouter API key found in $file"
            found_secrets=1
          fi

          # Check for OpenAI API keys
          if grep -qE "$OPENAI_PATTERN" "$file"; then
            echo "❌ SECURITY VIOLATION: OpenAI API key found in $file"
            found_secrets=1
          fi

          # Check for JWT tokens
          if grep -qE "$JWT_PATTERN" "$file"; then
            echo "❌ SECURITY VIOLATION: JWT token found in $file"
            found_secrets=1
          fi

          # Check for Supabase keys
          if grep -qE "$SUPABASE_KEY_PATTERN" "$file"; then
            echo "❌ SECURITY VIOLATION: Supabase key found in $file"
            found_secrets=1
          fi

          # Check for YouTube API keys
          if grep -qE "$YOUTUBE_KEY_PATTERN" "$file"; then
            echo "❌ SECURITY VIOLATION: YouTube API key found in $file"
            found_secrets=1
          fi

          # Check for generic secrets (case-insensitive)
          if grep -qiE "$GENERIC_SECRET_PATTERN" "$file"; then
            echo "⚠️  POTENTIAL SECRET: Generic secret pattern found in $file"
            # Don't fail on generic patterns, just warn
          fi

          return $found_secrets
        }

        # Scan all files in the repository
        total_violations=0
        while IFS= read -r -d '' file; do
          if check_secrets "$file"; then
            continue
          else
            ((total_violations++))
          fi
        done < <(find . -type f -not -path "./.git/*" -not -path "./node_modules/*" -not -path "./venv/*" -not -path "./**/node_modules/*" -print0)

        if [ $total_violations -gt 0 ]; then
          echo ""
          echo "🚨 SECURITY CHECK FAILED: $total_violations secret(s) detected!"
          echo "Please remove all secrets from the codebase and use environment variables instead."
          echo "Refer to docs/env-template.txt for proper configuration."
          exit 1
        else
          echo "✅ Security check passed: No secrets detected in codebase"
        fi

  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: writer_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Set up environment variables
      run: |
        cp .env.example .env
        echo "FLASK_APP=app.py" >> .env
        echo "FLASK_ENV=testing" >> .env
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/writer_test" >> .env
        echo "SECRET_KEY=test_secret_key" >> .env
        echo "JWT_SECRET_KEY=test_jwt_secret_key" >> .env
        echo "OPENAI_API_KEY=test_openai_api_key" >> .env
        echo "YOUTUBE_API_KEY=test_youtube_api_key" >> .env
    - name: Run tests
      run: |
        pytest

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 16
    - name: Install dependencies
      working-directory: ./frontend
      run: |
        npm ci
    - name: Run linting
      working-directory: ./frontend
      run: |
        npm run lint
    - name: Run unit tests
      working-directory: ./frontend
      run: |
        npm run test:coverage
    - name: Run API URL formation tests
      working-directory: ./frontend
      run: |
        npm run test:api
    - name: Run environment config tests
      working-directory: ./frontend
      run: |
        npm run test:env

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [security-check, backend-tests, frontend-tests]
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: writer_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 16

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Install Node.js dependencies
      working-directory: ./frontend
      run: |
        npm ci

    - name: Install Playwright browsers
      run: |
        npx playwright install --with-deps

    - name: Set up test environment
      run: |
        cp docs/env-template.txt .env.local
        # Use test values for environment
        echo "JWT_SECRET_KEY=test_jwt_secret_key" >> .env.local
        echo "OPENROUTER_API_KEY=test_openrouter_key" >> .env.local
        echo "SUPABASE_URL=https://test.supabase.co" >> .env.local
        echo "SUPABASE_KEY=test_supabase_key" >> .env.local
        echo "SUPABASE_SERVICE_ROLE_KEY=test_service_role_key" >> .env.local
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/writer_test" >> .env.local

    - name: Set up frontend test environment
      working-directory: ./frontend
      run: |
        cp ../.env.local .env.local
        echo "REACT_APP_API_URL=http://localhost:5000/api" >> .env.local
        echo "REACT_APP_SUPABASE_URL=https://test.supabase.co" >> .env.local
        echo "REACT_APP_SUPABASE_KEY=test_supabase_key" >> .env.local

    - name: Start backend server
      run: |
        python -m uvicorn app.main:app --host 0.0.0.0 --port 5000 &
        sleep 10

    - name: Start frontend server
      working-directory: ./frontend
      run: |
        npm start &
        sleep 15

    - name: Wait for servers to be ready
      run: |
        # Wait for backend health check
        timeout 60 bash -c 'until curl -f http://localhost:5000/health; do sleep 2; done'
        # Wait for frontend to be ready
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

    - name: Run Playwright E2E tests
      run: |
        npx playwright test playwright/tests/wizard-e2e-flow.test.js --reporter=list

    - name: Upload Playwright report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

  docker-build:
    runs-on: ubuntu-latest
    needs: [security-check, backend-tests, frontend-tests]
    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Pre-build Secret Detection
      run: |
        echo "🔍 Scanning Docker build context for secrets before building images..."

        # Define patterns for different types of secrets
        OPENROUTER_PATTERN="sk-or-v1-[a-zA-Z0-9]{64,}"
        OPENAI_PATTERN="sk-[a-zA-Z0-9]{48,}"
        JWT_PATTERN="[A-Za-z0-9_-]{100,}\.[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}"
        SUPABASE_KEY_PATTERN="eyJ[A-Za-z0-9_-]{100,}"
        YOUTUBE_KEY_PATTERN="AIza[A-Za-z0-9_-]{35}"

        # Function to check for secrets in Docker build context
        check_docker_context() {
          local violations=0

          # Check all files that would be included in Docker build context
          echo "Checking backend build context..."
          for file in requirements.txt app/ main.py supabase_health_check.py; do
            if [ -e "$file" ]; then
              if [ -f "$file" ]; then
                if grep -qE "$OPENROUTER_PATTERN|$OPENAI_PATTERN|$JWT_PATTERN|$SUPABASE_KEY_PATTERN|$YOUTUBE_KEY_PATTERN" "$file"; then
                  echo "❌ SECURITY VIOLATION: Secret detected in backend build context file: $file"
                  violations=1
                fi
              elif [ -d "$file" ]; then
                if find "$file" -type f -exec grep -lE "$OPENROUTER_PATTERN|$OPENAI_PATTERN|$JWT_PATTERN|$SUPABASE_KEY_PATTERN|$YOUTUBE_KEY_PATTERN" {} \; | head -1 | grep -q .; then
                  echo "❌ SECURITY VIOLATION: Secret detected in backend build context directory: $file"
                  violations=1
                fi
              fi
            fi
          done

          echo "Checking frontend build context..."
          if [ -d "frontend" ]; then
            # Check frontend files but exclude node_modules and build directories
            find frontend -type f -not -path "*/node_modules/*" -not -path "*/build/*" -not -path "*/.git/*" | while read -r file; do
              if grep -qE "$OPENROUTER_PATTERN|$OPENAI_PATTERN|$JWT_PATTERN|$SUPABASE_KEY_PATTERN|$YOUTUBE_KEY_PATTERN" "$file" 2>/dev/null; then
                echo "❌ SECURITY VIOLATION: Secret detected in frontend build context file: $file"
                violations=1
              fi
            done
          fi

          return $violations
        }

        if ! check_docker_context; then
          echo ""
          echo "🚨 DOCKER BUILD BLOCKED: Secrets detected in build context!"
          echo "Docker images must not contain any secrets or API keys."
          echo "All secrets should be provided via environment variables at runtime."
          exit 1
        else
          echo "✅ Docker build context security check passed"
        fi

    - name: Build Backend Docker Image
      run: |
        echo "🐳 Building backend Docker image..."
        docker build -f Dockerfile.backend -t writer-v2-backend:${{ github.sha }} .

    - name: Build Frontend Docker Image
      run: |
        echo "🐳 Building frontend Docker image..."
        docker build -f Dockerfile.frontend.prod -t writer-v2-frontend:${{ github.sha }} .

    - name: Post-build Secret Detection in Images
      run: |
        echo "🔍 Scanning built Docker images for secrets..."

        # Function to scan Docker image for secrets
        scan_image_for_secrets() {
          local image_name="$1"
          local violations=0

          echo "Scanning image: $image_name"

          # Create a temporary container to examine the filesystem
          container_id=$(docker create "$image_name")

          # Export the container filesystem
          docker export "$container_id" > /tmp/image_export.tar

          # Extract and scan the filesystem
          mkdir -p /tmp/image_scan
          tar -xf /tmp/image_export.tar -C /tmp/image_scan

          # Scan for secrets in the extracted filesystem
          if find /tmp/image_scan -type f -exec grep -lE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" {} \; 2>/dev/null | head -1 | grep -q .; then
            echo "❌ SECURITY VIOLATION: Secrets found in Docker image $image_name"
            violations=1
          fi

          # Cleanup
          docker rm "$container_id"
          rm -rf /tmp/image_scan /tmp/image_export.tar

          return $violations
        }

        # Scan both images
        total_violations=0

        if ! scan_image_for_secrets "writer-v2-backend:${{ github.sha }}"; then
          ((total_violations++))
        fi

        if ! scan_image_for_secrets "writer-v2-frontend:${{ github.sha }}"; then
          ((total_violations++))
        fi

        if [ $total_violations -gt 0 ]; then
          echo ""
          echo "🚨 DOCKER IMAGE SECURITY FAILURE: $total_violations image(s) contain secrets!"
          echo "Images with secrets cannot be deployed to production."
          exit 1
        else
          echo "✅ Docker images passed security scan - no secrets detected"
        fi

    - name: Test Docker Images
      run: |
        echo "🧪 Testing Docker images..."

        # Test backend image
        echo "Testing backend image..."
        docker run --rm -d --name test-backend -p 5001:5000 \
          -e JWT_SECRET_KEY=test_jwt_secret \
          -e OPENROUTER_API_KEY=test_api_key \
          -e SUPABASE_URL=https://test.supabase.co \
          -e SUPABASE_KEY=test_supabase_key \
          -e SUPABASE_SERVICE_ROLE_KEY=test_service_role_key \
          writer-v2-backend:${{ github.sha }}

        # Wait for backend to start
        sleep 10

        # Test backend health endpoint
        if curl -f http://localhost:5001/health; then
          echo "✅ Backend image health check passed"
        else
          echo "❌ Backend image health check failed"
          docker logs test-backend
          exit 1
        fi

        # Stop backend container
        docker stop test-backend

        # Test frontend image
        echo "Testing frontend image..."
        docker run --rm -d --name test-frontend -p 3001:80 \
          -e REACT_APP_API_URL=http://localhost:5001/api \
          -e REACT_APP_SUPABASE_URL=https://test.supabase.co \
          -e REACT_APP_SUPABASE_KEY=test_supabase_key \
          writer-v2-frontend:${{ github.sha }}

        # Wait for frontend to start
        sleep 5

        # Test frontend accessibility
        if curl -f http://localhost:3001; then
          echo "✅ Frontend image accessibility test passed"
        else
          echo "❌ Frontend image accessibility test failed"
          docker logs test-frontend
          exit 1
        fi

        # Stop frontend container
        docker stop test-frontend

        echo "✅ All Docker image tests passed"

    - name: Clean up test images
      if: always()
      run: |
        docker rmi writer-v2-backend:${{ github.sha }} || true
        docker rmi writer-v2-frontend:${{ github.sha }} || true