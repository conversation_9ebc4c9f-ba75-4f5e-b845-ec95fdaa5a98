name: PR Gate - Quality Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

jobs:
  pr-quality-gate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
        
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 16
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Install Node.js dependencies
      working-directory: ./frontend
      run: |
        npm ci
        
    - name: Run Backend Linting
      run: |
        echo "🔍 Running backend linting..."
        flake8 app/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: Run Backend Code Formatting Check
      run: |
        echo "🎨 Checking backend code formatting..."
        black --check app/ main.py supabase_health_check.py
        
    - name: Run Frontend Linting
      working-directory: ./frontend
      run: |
        echo "🔍 Running frontend linting..."
        npm run lint
        
    - name: Run Frontend Tests
      working-directory: ./frontend
      run: |
        echo "🧪 Running frontend tests..."
        npm run test:coverage
        npm run test:api
        npm run test:env
        
    - name: Run Backend Tests
      run: |
        echo "🧪 Running backend tests..."
        pytest -v --tb=short
        
    - name: Security Check - Secrets Detection
      run: |
        echo "🔒 Running security checks..."
        bash scripts/pre-commit-secrets-check.sh
        
    - name: PR Quality Summary
      if: always()
      run: |
        echo "📊 PR Quality Gate Summary"
        echo "=========================="
        echo "✅ Backend linting: ${{ steps.backend-lint.outcome || 'completed' }}"
        echo "✅ Backend formatting: ${{ steps.backend-format.outcome || 'completed' }}"
        echo "✅ Frontend linting: ${{ steps.frontend-lint.outcome || 'completed' }}"
        echo "✅ Frontend tests: ${{ steps.frontend-tests.outcome || 'completed' }}"
        echo "✅ Backend tests: ${{ steps.backend-tests.outcome || 'completed' }}"
        echo "✅ Security checks: ${{ steps.security-check.outcome || 'completed' }}"
        echo ""
        echo "🎯 All quality gates must pass before this PR can be merged."

  pr-build-verification:
    runs-on: ubuntu-latest
    needs: pr-quality-gate
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
      
    - name: Verify Docker Build Context Security
      run: |
        echo "🔍 Verifying Docker build context is secure..."
        
        # Check for secrets in files that would be copied to Docker images
        violations=0
        
        # Backend context check
        echo "Checking backend build context..."
        for file in requirements.txt main.py supabase_health_check.py; do
          if [ -f "$file" ]; then
            if grep -qE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" "$file"; then
              echo "❌ Secret detected in backend build context: $file"
              violations=1
            fi
          fi
        done
        
        # App directory check
        if [ -d "app" ]; then
          if find app -type f -exec grep -lE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" {} \; | head -1 | grep -q .; then
            echo "❌ Secret detected in backend app directory"
            violations=1
          fi
        fi
        
        # Frontend context check (excluding node_modules and build)
        echo "Checking frontend build context..."
        if [ -d "frontend" ]; then
          find frontend -type f -not -path "*/node_modules/*" -not -path "*/build/*" -not -path "*/.git/*" | while read -r file; do
            if grep -qE "sk-or-v1-[a-zA-Z0-9]{64,}|sk-[a-zA-Z0-9]{48,}|eyJ[A-Za-z0-9_-]{100,}|AIza[A-Za-z0-9_-]{35}" "$file" 2>/dev/null; then
              echo "❌ Secret detected in frontend build context: $file"
              violations=1
            fi
          done
        fi
        
        if [ $violations -eq 1 ]; then
          echo ""
          echo "🚨 PR BLOCKED: Secrets detected in Docker build context!"
          echo "Remove all secrets from source code before merging."
          echo "Use environment variables for secrets at runtime."
          exit 1
        else
          echo "✅ Docker build context security verification passed"
        fi
        
    - name: Test Docker Builds
      run: |
        echo "🐳 Testing Docker image builds..."
        
        # Test backend build
        echo "Testing backend Docker build..."
        if docker build -f Dockerfile.backend -t test-backend . > /dev/null 2>&1; then
          echo "✅ Backend Docker build successful"
          docker rmi test-backend
        else
          echo "❌ Backend Docker build failed"
          exit 1
        fi
        
        # Test frontend build
        echo "Testing frontend Docker build..."
        if docker build -f Dockerfile.frontend.prod -t test-frontend . > /dev/null 2>&1; then
          echo "✅ Frontend Docker build successful"
          docker rmi test-frontend
        else
          echo "❌ Frontend Docker build failed"
          exit 1
        fi
        
        echo "✅ All Docker builds completed successfully"

  pr-status-check:
    runs-on: ubuntu-latest
    needs: [pr-quality-gate, pr-build-verification]
    if: always()
    steps:
    - name: Check PR Status
      run: |
        echo "🎯 PR Status Check"
        echo "=================="
        
        quality_status="${{ needs.pr-quality-gate.result }}"
        build_status="${{ needs.pr-build-verification.result }}"
        
        echo "Quality Gate: $quality_status"
        echo "Build Verification: $build_status"
        
        if [ "$quality_status" = "success" ] && [ "$build_status" = "success" ]; then
          echo ""
          echo "✅ PR READY FOR MERGE"
          echo "All quality gates and build verifications have passed."
          exit 0
        else
          echo ""
          echo "❌ PR NOT READY FOR MERGE"
          echo "One or more quality gates have failed."
          echo "Please fix the issues and push new commits."
          exit 1
        fi
